# iOS-Optimized .gitignore for React Native Expo

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native builds (generated by Expo)
ios/
android/

# Build outputs
build/
*.tsbuildinfo

# Environment files
.env*.local
.env

# macOS
.DS_Store
*.pem

# iOS specific
*.mobileprovision
*.p8
*.p12
*.key

# Android specific (excluded for iOS-only repo)
*.jks
*.keystore

# Logs
*.log

# Temporary files
*.tmp
*.temp

# IDE
.vscode/
.idea/

# Metro
.metro-health-check*

# Testing
coverage/
.nyc_output/

# Cache
.cache/
node_modules/.cache/
