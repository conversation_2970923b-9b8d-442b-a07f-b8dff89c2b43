// Define the structure of our translations
export interface Translations {
  // Alert translations
  alerts: {
    success: {
      title: string;
      profileUpdated: string;
      accountCreated: string;
      passwordReset: string;
      photoUpdated: string;
    };
    error: {
      title: string;
      generic: string;
      networkError: string;
      invalidCredentials: string;
      uploadError: string;
    };
    warning: {
      title: string;
      deletePhotos: string;
      deleteConfirmation: string;
    };
    network: {
      title: string;
      noConnection: string;
      operationRequiresInternet: string;
    };
    buttons: {
      ok: string;
      cancel: string;
      confirm: string;
      tryAgain: string;
      yes: string;
      no: string;
    };
  };
  // Network status translations
  network: {
    connected: {
      wifi: string;
      cellular: string;
      ethernet: string;
      bluetooth: string;
      unknown: string;
    };
    disconnected: {
      noConnection: string;
      noInternet: string;
    };
  };
  // Common translations
  success: string;
  error: string;
  yes: string;
  no: string;
  ok: string;
  // Profile translations
  profileUpdated: string;
  updateProfileError: string;
  passwordResetSent: string;
  deleteAccountTitle: string;
  deleteAccountConfirm: string;
  accountDeleted: string;
  deleteAccountError: string;
  errorUploadingPhoto: string;
  editProfile: string;
  save: string;
  cancel: string;
  logout: string;
  resetPassword: string;
  deleteAccount: string;
  firstName: string;
  lastName: string;
  email: string;
  memberSince: string;
  required: string;
  photoOptions: string;
  takePhoto: string;
  choosePhoto: string;
  cancelPhoto: string;
  permissionDenied: string;
  // Reports translations
  deleteReport: string;
  confirmDeleteReport: string;
  reportDeleted: string;
  deleteReportError: string;
  deleteImages: string;
  confirmDeleteImages: string;
  imagesDeleted: string;
  deleteImagesError: string;
  noImagesSelected: string;
  deleteSelected: string;
  cancelSelection: string;
  // Alert Buttons
  confirm: string;
  close: string;
}

export const translations: Translations = {
  alerts: {
    success: {
      title: 'Sucesso',
      profileUpdated: 'Perfil atualizado',
      accountCreated: 'Conta criada com sucesso',
      passwordReset: 'Email de redefinição enviado',
      photoUpdated: 'Foto atualizada com sucesso',
    },
    error: {
      title: 'Erro',
      generic: 'Ocorreu um erro. Por favor, tente novamente.',
      networkError: 'Erro de conexão. Verifique sua internet.',
      invalidCredentials: 'Email ou senha inválidos',
      uploadError: 'Erro ao carregar a foto',
    },
    warning: {
      title: 'Atenção',
      deletePhotos: 'Eliminar fotografias',
      deleteConfirmation: 'Tem a certeza que pretende eliminar as fotografias selecionadas?',
    },
    network: {
      title: 'Sem ligação à Internet',
      noConnection: 'Sem ligação à Internet',
      operationRequiresInternet: 'Não é possível realizar esta operação sem ligação à Internet. Por favor, verifique a sua ligação e tente novamente.',
    },
    buttons: {
      ok: 'OK',
      cancel: 'Cancelar',
      confirm: 'Confirmar',
      tryAgain: 'Tentar Novamente',
      yes: 'Sim',
      no: 'Não',
    }
  },
  // Network
  network: {
    connected: {
      wifi: 'Wi-Fi',
      cellular: 'Dados móveis',
      ethernet: 'Ethernet',
      bluetooth: 'Bluetooth',
      unknown: 'Desconhecido',
    },
    disconnected: {
      noConnection: 'Sem ligação',
      noInternet: 'Sem Internet',
    },
  },
  // Common
  success: 'Êxito',
  error: 'Erro',
  yes: 'Sim',
  no: 'Não',
  ok: 'OK',
  // Profile
  profileUpdated: 'Perfil atualizado com êxito.',
  updateProfileError: 'Erro ao atualizar o perfil.',
  passwordResetSent: 'Email de redefinição de palavra-passe enviado.',
  deleteAccountTitle: 'Eliminar Conta',
  deleteAccountConfirm: 'Tem a certeza que pretende eliminar a sua conta?',
  accountDeleted: 'Conta eliminada com êxito.',
  deleteAccountError: 'Erro ao eliminar a conta.',
  errorUploadingPhoto: 'Erro ao carregar a fotografia.',
  editProfile: 'Editar Perfil',
  save: 'Guardar',
  cancel: 'Cancelar',
  logout: 'Terminar Sessão',
  resetPassword: 'Redefinir Palavra-passe',
  deleteAccount: 'Eliminar Conta',
  firstName: 'Nome',
  lastName: 'Apelido',
  email: 'Email',
  memberSince: 'Membro desde',
  required: 'Campo obrigatório',
  photoOptions: 'Foto de Perfil',
  takePhoto: 'Tirar Foto',
  choosePhoto: 'Escolher da Galeria',
  cancelPhoto: 'Cancelar',
  permissionDenied: 'Permissão negada para acessar a câmera ou galeria.',
  // Reports
  deleteReport: 'Eliminar Relatório',
  confirmDeleteReport: 'Tem a certeza que pretende eliminar este relatório?',
  reportDeleted: 'Relatório eliminado com êxito.',
  deleteReportError: 'Erro ao eliminar o relatório.',
  deleteImages: 'Eliminar Fotografias',
  confirmDeleteImages: 'Tem a certeza que pretende eliminar as fotografias selecionadas?',
  imagesDeleted: 'Fotografias eliminadas com êxito.',
  deleteImagesError: 'Erro ao eliminar as fotografias.',
  noImagesSelected: 'Nenhuma fotografia selecionada.',
  deleteSelected: 'Eliminar',
  cancelSelection: 'Cancelar',
  // Alert Buttons
  confirm: 'Confirmar',
  close: 'Fechar',
};

type TranslationKey = keyof Translations | `alerts.${keyof Translations['alerts']}.${string}` | `network.${keyof Translations['network']}.${string}`;

// Helper function to get nested translations
export const t = (key: TranslationKey): string => {
  const keys = key.split('.');
  let value: any = translations;
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      console.warn(`Translation key not found: ${key}`);
      return key;
    }
  }
  
  return value;
}; 