// Weather API configuration
// To use weather functionality, you need to:
// 1. Sign up for a free account at https://openweathermap.org/api
// 2. Get your API key from the dashboard
// 3. Replace the empty string below with your API key
// 
// Note: This app uses OpenWeatherMap's One Call API 3.0 for enhanced weather data
// including UV index, moon phase, and detailed atmospheric conditions ideal for bird watching.
// If One Call API is not available, it falls back to the basic Current Weather API.

export const WEATHER_CONFIG = {
  // Add your OpenWeatherMap API key here
  API_KEY: '********************************', // Replace with your actual API key
  
  // API endpoints
  BASE_URL: 'https://api.openweathermap.org/data/2.5/weather', // Fallback API
  ONE_CALL_URL: 'https://api.openweathermap.org/data/3.0/onecall', // Enhanced API
  ICON_BASE_URL: 'https://openweathermap.org/img/wn/',
  
  // Default settings
  UNITS: 'metric', // metric, imperial, or kelvin
  LANGUAGE: 'pt', // Portuguese
  
  // Request timeout in milliseconds (reduced from 10000 to prevent app loading issues)
  TIMEOUT: 5000,
};

// Helper function to check if weather API is configured
export const isWeatherConfigured = (): boolean => {
  return WEATHER_CONFIG.API_KEY.length > 0;
};

// Instructions for setting up OpenWeatherMap API
export const SETUP_INSTRUCTIONS = `
To enable weather functionality:

1. Visit https://openweathermap.org/api
2. Sign up for a free account
3. Go to your dashboard and copy your API key
4. Open config/weather.ts
5. Replace the empty API_KEY string with your key
6. Save the file and restart the app

The free tier includes:
- 1,000 API calls per day
- Current weather data
- 5-day weather forecast
- Weather maps

This is sufficient for most reporting needs.
`; 