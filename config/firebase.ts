import { initializeApp } from 'firebase/app';
import { initializeAuth, getReactNativePersistence } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Your web app's Firebase configuration prorola . <EMAIL>
const firebaseConfig = {
  apiKey: "AIzaSyAIOTTQiP22chvCkD8q4EwTBnXVNAIa5is",
  authDomain: "prorola-a2f66.firebaseapp.com",
  projectId: "prorola-a2f66",
  storageBucket: "prorola-a2f66.firebasestorage.app",
  messagingSenderId: "1068561348216",
  appId: Platform.OS === 'ios' 
    ? "1:1068561348216:ios:41ebfaf826844588395f8d"     // iOS App ID
    : "1:1068561348216:android:1d6300ea0f33c842395f8d", // Android App ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services with AsyncStorage persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage)
});
const db = getFirestore(app);
const storage = getStorage(app);

export { auth, storage, db };
export default app; 