import { useState, useCallback, useEffect, useRef } from 'react';

export interface AlertConfig {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  title?: string;
  onConfirm?: () => void;
}

export function useCustomAlert() {
  const [isVisible, setIsVisible] = useState(false);
  const [config, setConfig] = useState<AlertConfig>({
    type: 'success',
    message: '',
  });
  const alertTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }
    };
  }, []);

  const showAlert = useCallback((newConfig: AlertConfig) => {
    // Clear any existing timeout
    if (alertTimeoutRef.current) {
      clearTimeout(alertTimeoutRef.current);
    }
    
    setConfig(newConfig);
    setIsVisible(true);
    
    // Auto-hide success alerts after 3 seconds to prevent lingering
    // But only if they don't have an onConfirm callback (which requires user interaction)
    if (newConfig.type === 'success' && !newConfig.onConfirm) {
      alertTimeoutRef.current = setTimeout(() => {
        setIsVisible(false);
      }, 3000);
    }
  }, []);

  const hideAlert = useCallback(() => {
    if (alertTimeoutRef.current) {
      clearTimeout(alertTimeoutRef.current);
    }
    setIsVisible(false);
  }, []);

  return {
    isVisible,
    config,
    showAlert,
    hideAlert,
  };
} 