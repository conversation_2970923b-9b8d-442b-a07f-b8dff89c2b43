{"cli": {"version": ">= 5.9.1", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "env": {"NODE_ENV": "development"}, "ios": {"buildConfiguration": "Debug"}, "android": {"gradleCommand": ":app:assembleDebug"}}, "device": {"developmentClient": true, "distribution": "internal", "env": {"NODE_ENV": "development"}, "ios": {"buildConfiguration": "Debug"}}, "standalone": {"distribution": "internal", "env": {"NODE_ENV": "development"}, "ios": {"buildConfiguration": "Release"}}, "preview": {"distribution": "internal", "ios": {"simulator": false}, "android": {"buildType": "apk"}}, "simulator": {"ios": {"simulator": true}}, "production": {"env": {"NODE_ENV": "production"}, "android": {"buildType": "app-bundle"}, "ios": {"buildConfiguration": "Release"}}}, "submit": {"production": {"android": {"track": "production"}, "ios": {"appleId": "<EMAIL>", "ascAppId": "1234567890", "appleTeamId": "XXXXXXXXXX"}}}}