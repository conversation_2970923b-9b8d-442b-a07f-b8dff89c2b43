# ProROLA iOS

iOS-optimized repository for ProROLA React Native Expo application, configured for Xcode Cloud builds.

## Overview

This repository contains only the essential files needed for iOS builds, optimized for Xcode Cloud integration. It excludes web admin components, documentation, and Android-specific files to minimize repository size and build times.

## Repository Structure

```
├── app/                    # Expo Router app directory
├── components/             # React components
├── constants/              # App constants
├── contexts/               # React contexts
├── hooks/                  # Custom hooks
├── services/               # App services
├── types/                  # TypeScript types
├── utils/                  # Utility functions
├── assets/                 # Images, fonts, icons
├── config/                 # Firebase and other configs
├── ci_scripts/             # Xcode Cloud build scripts
├── App.tsx                 # Main app component
├── app.json                # Expo configuration
├── eas.json                # EAS Build configuration
├── package.json            # Dependencies and scripts
└── README.md               # This file
```

## Prerequisites

- Node.js 18 or later
- Expo CLI
- Xcode (for local iOS builds)
- Apple Developer Account (for Xcode Cloud)

## Local Development

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm start
   ```

3. **Run on iOS simulator:**
   ```bash
   npm run ios
   ```

## Xcode Cloud Integration

This repository is configured for Xcode Cloud with the following build scripts:

- `ci_scripts/ci_post_clone.sh` - Sets up Node.js, installs dependencies, and prebuilds iOS project
- `ci_scripts/ci_pre_xcodebuild.sh` - Installs CocoaPods dependencies
- `ci_scripts/ci_post_xcodebuild.sh` - Post-build processing and validation

### Xcode Cloud Setup

1. Connect this repository to Xcode Cloud
2. Configure build workflow for iOS
3. Set up signing certificates and provisioning profiles
4. Configure environment variables if needed

## Build Configuration

- **Bundle Identifier:** `com.prorola.app`
- **Expo SDK:** 52.x
- **React Native:** 0.76.x
- **Firebase:** Configured for iOS
- **Google Maps:** Integrated with API key

## Key Features

- Firebase Authentication
- Firestore Database
- Firebase Storage
- Google Maps Integration
- Location Services
- Image Picker
- Push Notifications (configured)
- Internationalization (Portuguese)

## Repository Optimization

This repository is optimized for iOS builds by excluding:
- Web admin components (~200MB)
- Documentation files (~50MB)
- Android-specific files (~100MB)
- Development artifacts (~150MB)

**Size Reduction:** ~80-90% compared to full repository

## Support

For build issues or questions, check the Xcode Cloud build logs and ensure all dependencies are properly configured.
