export interface MovementThreshold {
  distance: number;
  name: string;
}

export interface LocationFilterConfig {
  maxAccuracy: number;
  maxSpeed: number;
  minTimeInterval: number;
  maxJumpDistance: number;        // Maximum allowed distance jump (meters)
  maxJumpSpeed: number;           // Maximum allowed instantaneous speed (m/s)
  spikeDetectionThreshold: number; // Threshold for spike detection (meters)
  accuracyConfidence: number;     // Z-score for accuracy gating (1.8 = ~90% confidence)
  autoPauseSpeed: number;         // Speed threshold for auto-pause detection (m/s)
  autoPauseTime: number;          // Time threshold for auto-pause detection (ms)
}

/**
 * Get adaptive movement thresholds based on detected speed/activity
 * @param speed Current speed in m/s
 * @returns Movement threshold configuration
 */
export const getMovementThresholds = (speed: number): MovementThreshold => {
  if (speed < 0.5) return { distance: 3, name: 'Stationary' };      // 3m - stationary/very slow walking
  if (speed < 2.0) return { distance: 5, name: 'Walking' };         // 5m - normal walking
  if (speed < 6.0) return { distance: 8, name: 'Cycling' };         // 8m - cycling/jogging
  return { distance: 15, name: 'Driving' };                         // 15m - driving/fast movement
};

/**
 * Default configuration for location filtering
 */
export const DEFAULT_FILTER_CONFIG: LocationFilterConfig = {
  maxAccuracy: 25,        // Reject GPS points with accuracy worse than 25m (stricter)
  maxSpeed: 50,           // Reject GPS points with unrealistic speed (>50 m/s = 180 km/h)
  minTimeInterval: 1000,  // Minimum time between valid points (1 second for better responsiveness)
  maxJumpDistance: 500,   // Maximum allowed distance jump in meters (prevents 30km+ spikes)
  maxJumpSpeed: 25,       // Maximum allowed instantaneous speed (90 km/h)
  spikeDetectionThreshold: 100, // Distance threshold for spike detection (meters)
  accuracyConfidence: 1.8, // Z-score for 90% confidence interval in accuracy gating
  autoPauseSpeed: 0.5,    // Auto-pause when speed < 0.5 m/s (1.8 km/h)
  autoPauseTime: 8000,    // Auto-pause after 8 seconds of low speed
};

/**
 * Calculate distance between two GPS points using Haversine formula
 * @param lat1 Latitude of first point
 * @param lng1 Longitude of first point
 * @param lat2 Latitude of second point
 * @param lng2 Longitude of second point
 * @returns Distance in meters
 */
export const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI/180;
  const φ2 = lat2 * Math.PI/180;
  const Δφ = (lat2-lat1) * Math.PI/180;
  const Δλ = (lng2-lng1) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
};

/**
 * Calculate speed between two GPS points
 * @param distance Distance in meters
 * @param timeInterval Time interval in milliseconds
 * @returns Speed in m/s
 */
export const calculateSpeed = (distance: number, timeInterval: number): number => {
  return distance / (timeInterval / 1000);
};

/**
 * Calculate combined GPS accuracy gate using uncertainty propagation
 * This accounts for the uncertainty in both the previous and current GPS readings
 * @param prevAccuracy Previous GPS reading accuracy (meters)
 * @param currAccuracy Current GPS reading accuracy (meters)
 * @param confidence Z-score for confidence interval (1.8 = ~90%, 2.0 = ~95%)
 * @returns Combined accuracy gate threshold (meters)
 */
export const calculateAccuracyGate = (
  prevAccuracy: number | null | undefined,
  currAccuracy: number | null | undefined,
  confidence: number = 1.8
): number => {
  // Use reasonable defaults if accuracy is not available
  const prev = prevAccuracy || 10; // Default 10m if unknown
  const curr = currAccuracy || 10; // Default 10m if unknown
  
  // Combined uncertainty using root sum of squares
  // This represents the radius within which the true movement could be noise
  const combinedUncertainty = Math.sqrt(prev * prev + curr * curr);
  
  // Apply confidence interval (1.8 = ~90% confidence that movement is real)
  return confidence * combinedUncertainty;
};

/**
 * Determine if a GPS point should be recorded based on advanced accuracy-gating
 * @param distance Distance from last recorded point (meters)
 * @param speed Current calculated speed (m/s)
 * @param accuracy GPS accuracy (meters) - optional
 * @param prevAccuracy Previous GPS accuracy (meters) - optional
 * @param timeInterval Time since last point (milliseconds)
 * @param config Filter configuration
 * @returns true if point should be recorded
 */
export const shouldRecordGPSPoint = (
  distance: number,
  speed: number,
  accuracy?: number | null,
  timeInterval?: number,
  config: LocationFilterConfig = DEFAULT_FILTER_CONFIG,
  prevAccuracy?: number | null
): boolean => {
  // Filter 1: GPS accuracy filter (reject poor accuracy readings early)
  if (accuracy && accuracy > config.maxAccuracy) {
    if (Math.random() < 0.2) console.log(`❌ GPS: Poor accuracy ${accuracy.toFixed(1)}m`);
    return false;
  }
  
  // Filter 2: Speed validation (reject unrealistic speeds)
  if (speed > config.maxSpeed) {
    console.log(`❌ GPS: Unrealistic speed ${speed.toFixed(1)}m/s`);
    return false;
  }
  
  // Filter 3: Time interval filter (prevent too frequent updates)
  if (timeInterval && timeInterval < config.minTimeInterval) {
    // Only log occasionally to reduce spam
    if (Math.random() < 0.1) console.log(`❌ GPS: Too frequent (${timeInterval}ms)`);
    return false;
  }
  
  // Filter 4: GPS Spike Detection (CRITICAL - prevents 30km+ jumps)
  if (distance > config.maxJumpDistance) {
    console.warn(`🚨 GPS spike detected: ${distance.toFixed(1)}m jump rejected (max allowed: ${config.maxJumpDistance}m)`);
    return false;
  }
  
  // Filter 5: Instantaneous speed validation (prevents impossible speeds)
  if (timeInterval && timeInterval > 0) {
    const instantaneousSpeed = (distance / timeInterval) * 1000; // Convert to m/s
    if (instantaneousSpeed > config.maxJumpSpeed) {
      console.warn(`🚨 Impossible speed detected: ${instantaneousSpeed.toFixed(1)}m/s (${(instantaneousSpeed * 3.6).toFixed(1)}km/h) rejected`);
      return false;
    }
  }
  
  // Filter 6: ACCURACY-GATING - Combined uncertainty analysis
  const accuracyGate = calculateAccuracyGate(prevAccuracy, accuracy, config.accuracyConfidence);
  const speedGate = getMovementThresholds(speed).distance;
  const combinedGate = Math.max(accuracyGate, speedGate);
  
  if (distance < combinedGate) {
    // Only log rejections for significant movements or occasionally for debugging
    if (distance > 5 || Math.random() < 0.1) {
      console.log(`❌ GPS: Rejected - movement ${distance.toFixed(1)}m < gate ${combinedGate.toFixed(1)}m`);
    }
    return false;
  }
  
  console.log(`✅ GPS: Accepted - movement ${distance.toFixed(1)}m`);
  return true;
};

/**
 * Determine if bearing should be updated based on movement
 * @param distance Distance from last position (meters)
 * @param speed Current speed (m/s)
 * @returns true if bearing should be updated
 */
export const shouldUpdateBearing = (distance: number, speed: number): boolean => {
  const minMovementDistance = 5; // meters
  const minMovementSpeed = 0.5; // m/s (~1.8 km/h walking speed)
  
  return distance >= minMovementDistance && speed >= minMovementSpeed;
};

/**
 * Detect GPS spikes and provide detailed analysis
 * @param distance Distance from last position (meters)
 * @param speed Current speed (m/s)
 * @param accuracy GPS accuracy (meters)
 * @param timeInterval Time since last update (milliseconds)
 * @param config Filter configuration
 * @returns Analysis of potential GPS spike
 */
export const analyzeGPSSpike = (
  distance: number,
  speed: number,
  accuracy?: number | null,
  timeInterval?: number,
  config: LocationFilterConfig = DEFAULT_FILTER_CONFIG
): {
  isSpike: boolean;
  reason: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
} => {
  // Critical spike: 30km+ jumps
  if (distance > 30000) {
    return {
      isSpike: true,
      reason: `Extreme GPS jump: ${(distance/1000).toFixed(1)}km`,
      severity: 'critical',
      recommendation: 'Reject - likely GPS error or satellite switch'
    };
  }
  
  // High spike: 1-30km jumps
  if (distance > 1000) {
    return {
      isSpike: true,
      reason: `Large GPS jump: ${(distance/1000).toFixed(1)}km`,
      severity: 'high',
      recommendation: 'Reject - likely GPS error'
    };
  }
  
  // Medium spike: 500m-1km jumps
  if (distance > config.maxJumpDistance) {
    return {
      isSpike: true,
      reason: `Moderate GPS jump: ${distance.toFixed(0)}m`,
      severity: 'medium',
      recommendation: 'Reject - exceeds maximum jump distance'
    };
  }
  
  // Speed-based spike detection
  if (timeInterval && timeInterval > 0) {
    const instantaneousSpeed = (distance / timeInterval) * 1000; // m/s
    const speedKmh = instantaneousSpeed * 3.6;
    
    if (speedKmh > 200) {
      return {
        isSpike: true,
        reason: `Impossible speed: ${speedKmh.toFixed(1)}km/h`,
        severity: 'critical',
        recommendation: 'Reject - physically impossible speed'
      };
    }
    
    if (speedKmh > 100) {
      return {
        isSpike: true,
        reason: `High speed: ${speedKmh.toFixed(1)}km/h`,
        severity: 'high',
        recommendation: 'Reject - unlikely for pedestrian monitoring'
      };
    }
  }
  
  // Poor accuracy spike
  if (accuracy && accuracy > 50 && distance > 100) {
    return {
      isSpike: true,
      reason: `Poor accuracy spike: ${accuracy.toFixed(0)}m accuracy, ${distance.toFixed(0)}m jump`,
      severity: 'medium',
      recommendation: 'Reject - poor GPS accuracy with large movement'
    };
  }
  
  return {
    isSpike: false,
    reason: 'Normal GPS reading',
    severity: 'low',
    recommendation: 'Accept'
  };
};

/**
 * Enhanced GPS point data structure
 */
export interface EnhancedGPSPoint {
  sessionId: string;
  timestamp: string;
  latitude: number;
  longitude: number;
  accuracy?: number | null;
  altitude?: number | null;
  heading?: number | null;
  speed?: number | null;
  distance: number;
  filteredSpeed: number;
  activityType: string;
  isFiltered: boolean;
}

/**
 * Create enhanced GPS point with filtering metadata
 * @param sessionId Monitoring session ID
 * @param location Location object from React Native Location
 * @param distance Calculated distance from last point
 * @param filteredSpeed Calculated speed from distance/time
 * @param isFiltered Whether this point passed filtering
 * @returns Enhanced GPS point object
 */
export const createEnhancedGPSPoint = (
  sessionId: string,
  location: any,
  distance: number,
  filteredSpeed: number,
  isFiltered: boolean
): EnhancedGPSPoint => {
  const threshold = getMovementThresholds(filteredSpeed);
  
  return {
    sessionId,
    timestamp: new Date().toISOString(),
    latitude: location.coords.latitude,
    longitude: location.coords.longitude,
    accuracy: location.coords.accuracy,
    altitude: location.coords.altitude,
    heading: location.coords.heading,
    speed: location.coords.speed,
    distance,
    filteredSpeed,
    activityType: threshold.name,
    isFiltered,
  };
}; 