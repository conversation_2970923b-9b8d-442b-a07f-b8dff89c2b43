import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Alert, ScrollView, Dimensions, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { Ionicons, FontAwesome } from '@expo/vector-icons';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../../../config/firebase';

interface ContactEvent {
  id: string;
  sessionId: string;
  timestamp: any;
  observerLocation: {
    latitude: number;
    longitude: number;
  };
  contactLocation: {
    latitude: number;
    longitude: number;
  };
  distance: number;
  bearing?: number;
  circumstances: any;
  contactLocationDetails: any;
  images?: string[];
}

interface SessionData {
  sessionId: string;
  pathCoordinates: Array<{
    latitude: number;
    longitude: number;
  }>;
  startTime: any;
  endTime: any;
  totalDistance: number;
}

interface TechnicianMapScreenProps {
  reportId: string;
  sessionId: string;
}

const TechnicianMapScreen: React.FC<TechnicianMapScreenProps> = ({ reportId, sessionId }) => {
  const [loading, setLoading] = useState(true);
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [contactEvents, setContactEvents] = useState<ContactEvent[]>([]);
  const [reportData, setReportData] = useState<any>(null);

  useEffect(() => {
    loadRouteData();
  }, [reportId, sessionId]);

  const loadRouteData = async () => {
    try {
      setLoading(true);

      // Get report data
      const reportDoc = await getDoc(doc(db, 'reports', reportId));
      if (reportDoc.exists()) {
        setReportData({ id: reportDoc.id, ...reportDoc.data() });
      }

      // Get session data from monitoringSessions collection
      const sessionsRef = collection(db, 'monitoringSessions');
      const sessionQuery = query(sessionsRef, where('sessionId', '==', sessionId));
      const sessionSnapshot = await getDocs(sessionQuery);
      
      if (!sessionSnapshot.empty) {
        const sessionDoc = sessionSnapshot.docs[0];
        setSessionData({ id: sessionDoc.id, ...sessionDoc.data() } as SessionData);
      }

      // Get contact events for this session
      const contactEventsRef = collection(db, 'contactEvents');
      const contactQuery = query(
        contactEventsRef, 
        where('sessionId', '==', sessionId)
      );
      const contactSnapshot = await getDocs(contactQuery);
      
      const events: ContactEvent[] = [];
      contactSnapshot.forEach((doc) => {
        events.push({ id: doc.id, ...doc.data() } as ContactEvent);
      });

      // Sort contact events by timestamp
      events.sort((a, b) => {
        const timeA = a.timestamp?.toDate ? a.timestamp.toDate().getTime() : new Date(a.timestamp).getTime();
        const timeB = b.timestamp?.toDate ? b.timestamp.toDate().getTime() : new Date(b.timestamp).getTime();
        return timeA - timeB;
      });

      setContactEvents(events);

    } catch (error) {
      console.error('Error loading route data:', error);
      Alert.alert('Erro', 'Não foi possível carregar os dados da rota.');
    } finally {
      setLoading(false);
    }
  };

  const getProtocolDisplayName = (protocol: string) => {
    const protocolNames: { [key: string]: string } = {
      'trajeto': 'Trajeto',
      'estacoes_escuta': 'Estações de escuta',
      'metodo_mapas': 'Método dos mapas',
      'contagens_pontos': 'Contagens em pontos',
      'captura_marcacao': 'Captura e marcação',
      'acompanhamento_cacadas': 'Acompanhamento de caçadas',
      'registos_ocasionais': 'Registos ocasionais'
    };
    return protocolNames[protocol] || protocol;
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0ea5e9" />
          <Text style={styles.loadingText}>Carregando dados da rota...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Calculate map region based on route path and contact events
  let region = {
    latitude: 39.5,
    longitude: -8.0,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  };

  if (sessionData?.pathCoordinates && sessionData.pathCoordinates.length > 0) {
    const coordinates = sessionData.pathCoordinates;
    const latitudes = coordinates.map(coord => coord.latitude);
    const longitudes = coordinates.map(coord => coord.longitude);
    
    const minLat = Math.min(...latitudes);
    const maxLat = Math.max(...latitudes);
    const minLng = Math.min(...longitudes);
    const maxLng = Math.max(...longitudes);
    
    region = {
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      latitudeDelta: (maxLat - minLat) * 1.5,
      longitudeDelta: (maxLng - minLng) * 1.5,
    };
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {/* Header Info */}
        <View style={styles.header}>
          <View style={styles.headerRow}>
            <View style={styles.protocolBadge}>
              <Ionicons name="route-outline" size={16} color="#0ea5e9" />
              <Text style={styles.protocolText}>
                {getProtocolDisplayName(reportData?.protocol || 'N/A')}
              </Text>
            </View>
            <Text style={styles.sessionId}>Sessão: {sessionId}</Text>
          </View>
          
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Ionicons name="people-outline" size={16} color="#6b7280" />
              <Text style={styles.statText}>
                {contactEvents.length} contactos
              </Text>
            </View>
            
            {sessionData && (
              <View style={styles.statItem}>
                <Ionicons name="time-outline" size={16} color="#6b7280" />
                <Text style={styles.statText}>
                  {formatDuration((sessionData.endTime?.toDate().getTime() - sessionData.startTime?.toDate().getTime()) / 1000 || 0)}
                </Text>
              </View>
            )}
            
            {sessionData?.totalDistance && (
              <View style={styles.statItem}>
                <Ionicons name="location-outline" size={16} color="#6b7280" />
                <Text style={styles.statText}>
                  {sessionData.totalDistance < 1000 
                    ? `${Math.round(sessionData.totalDistance)}m` 
                    : `${(sessionData.totalDistance / 1000).toFixed(2)}km`
                  }
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Map */}
        <View style={styles.mapContainer}>
          <MapView
            provider={PROVIDER_GOOGLE}
            style={styles.map}
            region={region}
            mapType="terrain"
          >
            {/* Route path */}
            {sessionData?.pathCoordinates && sessionData.pathCoordinates.length > 0 && (
              <Polyline
                coordinates={sessionData.pathCoordinates}
                strokeColor="#0ea5e9"
                strokeWidth={4}
                strokePattern={[1]}
              />
            )}

            {/* Start point - use monitoring start location if available, fallback to first GPS point */}
            {sessionData?.pathCoordinates && sessionData.pathCoordinates.length > 0 && (
              <Marker
                coordinate={sessionData.monitoringStartLocation || sessionData.pathCoordinates[0]}
                title="Início do trajeto"
              >
                <View style={styles.startMarker}>
                  <Ionicons name="play" size={16} color="white" />
                </View>
              </Marker>
            )}

            {/* End point */}
            {sessionData?.pathCoordinates && sessionData.pathCoordinates.length > 1 && (
              <Marker
                coordinate={sessionData.pathCoordinates[sessionData.pathCoordinates.length - 1]}
                title="Fim do trajeto"
              >
                <View style={styles.endMarker}>
                  <Ionicons name="stop" size={16} color="white" />
                </View>
              </Marker>
            )}

            {/* Contact events */}
            {contactEvents.map((contact, index) => (
              <React.Fragment key={contact.id}>
                {/* Observer position */}
                <Marker
                  coordinate={contact.observerLocation}
                  title={`Contacto ${index + 1} - Observador`}
                  description={`Posição do observador`}
                >
                  <View style={styles.observerMarker}>
                    <Text style={styles.markerText}>{index + 1}</Text>
                  </View>
                </Marker>

                {/* Contact position */}
                <Marker
                  coordinate={contact.contactLocation}
                  title={`Contacto ${index + 1} - Rola`}
                  description={`Distância: ${contact.distance < 1000 ? Math.round(contact.distance) + 'm' : (contact.distance / 1000).toFixed(2) + 'km'}`}
                >
                  <View style={styles.contactMarker}>
                    <Ionicons name={index % 2 === 0 ? "add-circle" : "remove-circle"} size={20} color="#FFFFFF" />
                  </View>
                </Marker>

                {/* Line from observer to contact */}
                <Polyline
                  coordinates={[contact.observerLocation, contact.contactLocation]}
                  strokeColor="#059669"
                  strokeWidth={2}
                  strokePattern={[10, 5]}
                />
              </React.Fragment>
            ))}
          </MapView>
        </View>

        {/* Contact Events List */}
        {contactEvents.length > 0 && (
          <View style={styles.contactsSection}>
            <Text style={styles.sectionTitle}>
              <Ionicons name="list-outline" size={18} color="#374151" />
              {' '}Contactos Registados ({contactEvents.length})
            </Text>
            
            {contactEvents.map((contact, index) => {
              const timestamp = contact.timestamp?.toDate ? contact.timestamp.toDate() : new Date(contact.timestamp);
              
              return (
                <View key={contact.id} style={styles.contactCard}>
                  <View style={styles.contactHeader}>
                    <View style={styles.contactNumber}>
                      <Text style={styles.contactNumberText}>{index + 1}</Text>
                    </View>
                    <Text style={styles.contactTime}>
                      {timestamp.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit', second: '2-digit' })}
                    </Text>
                  </View>
                  
                  <View style={styles.contactDetails}>
                    <View style={styles.contactDetail}>
                      <Ionicons name="location-outline" size={14} color="#6b7280" />
                      <Text style={styles.contactDetailText}>
                        Distância: {contact.distance < 1000 
                          ? `${Math.round(contact.distance)}m` 
                          : `${(contact.distance / 1000).toFixed(2)}km`
                        }
                      </Text>
                    </View>
                    
                    {contact.bearing && (
                      <View style={styles.contactDetail}>
                        <Ionicons name="compass-outline" size={14} color="#6b7280" />
                        <Text style={styles.contactDetailText}>
                          Direção: {Math.round(contact.bearing)}°
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              );
            })}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  header: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  protocolBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  protocolText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0ea5e9',
  },
  sessionId: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748b',
  },
  statsRow: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    color: '#6b7280',
  },
  mapContainer: {
    height: height * 0.5,
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  map: {
    flex: 1,
  },
  startMarker: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#10b981',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  endMarker: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#ef4444',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  observerMarker: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#0ea5e9',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  contactMarker: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#059669',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 4,
  },
  markerText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  contactsSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactCard: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  contactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  contactNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#059669',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactNumberText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  contactTime: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
  contactDetails: {
    gap: 4,
  },
  contactDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  contactDetailText: {
    fontSize: 12,
    color: '#6b7280',
  },
});

export default TechnicianMapScreen; 