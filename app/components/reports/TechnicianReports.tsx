import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  Modal,
  Platform,
  Image,
  Dimensions,
  ScrollView,
  Pressable,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useNetwork } from '@/contexts/NetworkContext';
import app, { db } from '@/config/firebase';
import {
  collection,
  query,
  where,
  orderBy,
  getDocs,
  Timestamp,
  deleteDoc,
  doc,
  onSnapshot,
  getFirestore,
  DocumentData,
  updateDoc,
} from 'firebase/firestore';
import { ref as storageRef, deleteObject } from 'firebase/storage';
import { storage } from '@/config/firebase';
// MapView imports no longer needed - using TechnicianMapScreen component
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import LoadingImage from '@/components/LoadingImage';
import TechnicianMapScreen from '../../../components/TechnicianMapScreen';
import { t } from '@/config/translations';

interface TechnicianReport extends DocumentData {
  id: string;
  userId: string;
  userName: string;
  userEmail?: string;
  userRole: 'tecnico_prorola';
  type: 'tecnico_monitoring_report' | 'monitoring_session_summary';
  // Core technician fields
  protocol: string; // Required for technician reports
  sessionId: string; // Required for technician reports
  reportName?: string; // Name of the report
  comment?: string;
  createdAt: Timestamp | null | undefined;
  images?: string[];
  location: {
    latitude: number;
    longitude: number;
  };
  // Device information - important for technician reports
  deviceInfo: {
    platform: string;
    model?: string;
    os?: string;
    version?: string;
  };
  // Weather conditions - critical for monitoring protocols
  weather?: {
    temperature: number;
    feelsLike: number;
    description: string;
    humidity: number;
    pressure: number;
    pressureTrend?: 'rising' | 'falling' | 'steady';
    windSpeed: number;
    windDirection: number;
    windGust?: number;
    visibility: number;
    cloudiness: number;
    uvIndex?: number;
    dewPoint?: number;
    icon: string;
    timestamp: number;
    sunrise: number;
    sunset: number;
    moonPhase?: number;
    barometricTrend?: string;
  };
  // Session management fields
  observersCount: number; // Required for monitoring sessions
  startTime: Timestamp | null | undefined; // Required for session reports
  endTime?: Timestamp | null | undefined; // May not be available if session is ongoing
  sessionDuration?: number; // Calculated field
  // Contact tracking
  contactEventsCount?: number; // Number of contact events in this session
  // Additional technician-specific fields
  monitoringConditions?: {
    visibility: 'excellent' | 'good' | 'fair' | 'poor';
    windConditions: 'calm' | 'light' | 'moderate' | 'strong';
    weatherSuitability: 'ideal' | 'suitable' | 'challenging' | 'unsuitable';
  };
}

const pt = {
  // Screen titles and navigation
  noReports: 'Não existem relatórios técnicos',
  noInternetReports: 'Não é possível carregar relatórios sem ligação à Internet',
  loading: 'A carregar relatórios...',
  error: 'Erro ao carregar relatórios',
  retry: 'Tentar novamente',
  
  // Report fields
  date: 'Data',
  location: 'Localização',
  comment: 'Observações',
  protocol: 'Protocolo',
  session: 'Sessão',
  duration: 'Duração',
  observers: 'Observadores',
  contacts: 'Contactos',
  device: 'Dispositivo',
  startTime: 'Início',
  endTime: 'Fim',
  
  // Actions
  view: 'Ver',
  delete: 'Eliminar',
  cancel: 'Cancelar',
  confirmDelete: 'Tem certeza que deseja eliminar este relatório?',
  yes: 'Sim',
  no: 'Não',
  reportOptions: 'Opções do Relatório',
  deleteSuccess: 'Relatório técnico eliminado com sucesso',
  deleteError: 'Erro ao eliminar relatório técnico',
  success: 'Sucesso',
  
  // Images
  viewImages: 'Ver Imagens',
  closeImages: 'Fechar',
  deleteImage: 'Eliminar imagem',
  confirmDeleteImage: 'Tem certeza que deseja eliminar esta imagem?',
  imageDeleteSuccess: 'Imagem eliminada com sucesso',
  imageDeleteError: 'Erro ao eliminar imagem',
  deleteSelected: 'Eliminar Selecionadas',
  cancelSelection: 'Cancelar',
  noImagesSelected: 'Selecione as imagens para eliminar',
  selectedCount: 'Selecionadas: ',
  
  // Protocol names
  protocolNames: {
    trajeto: 'Trajeto',
    estacoes_escuta: 'Estações de escuta',
    metodo_mapas: 'Método dos mapas',
    contagens_pontos: 'Contagens em pontos',
    captura_marcacao: 'Captura e marcação',
    acompanhamento_cacadas: 'Acompanhamento de caçadas',
    registos_ocasionais: 'Registos ocasionais'
  },
  
  // Weather and conditions
  weatherConditions: 'Condições Meteorológicas',
  temperature: 'Temperatura',
  humidity: 'Humidade',
  pressure: 'Pressão',
  windSpeed: 'Vento',
  visibility: 'Visibilidade',
  cloudiness: 'Nebulosidade',
  monitoringConditions: 'Condições de Monitorização',
  
  // Report types
  monitoringSession: 'Sessão de Monitorização',
  sessionReport: 'Relatório de Sessão',
  technicalReport: 'Relatório Técnico',
  
  // Device types
  deviceTypes: {
    android: 'Android',
    ios: 'iPhone',
    unknown: 'Dispositivo'
  },
  
  // Empty state
  noReportsOffline: 'Não é possível carregar relatórios técnicos sem ligação à Internet',
  noTechnicalReports: 'Sem relatórios',
  technicalReportsWillAppear: 'Os seus relatórios de monitorização aparecerão aqui quando forem criados',
  howToCreateTechnicalReport: 'Como criar um relatório técnico:',
  
  // Instructions
  goToLocationPage: 'Vá para a página de Localização',
  selectMonitoringProtocol: 'Selecione um protocolo de monitorização',
  setWeatherConditions: 'Defina as condições meteorológicas',
  startMonitoringSession: 'Inicie a sessão de monitorização',
  recordContactEvents: 'Registe eventos de contacto durante a sessão',
  endSessionAndUpload: 'Termine a sessão e carregue o relatório',
};

export default function TechnicianReportsScreen() {
  const { user } = useAuth();
  const { hasInternetConnection } = useNetwork();
  const [reports, setReports] = useState<TechnicianReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedReport, setSelectedReport] = useState<TechnicianReport | null>(null);
  const [isMapModalVisible, setMapModalVisible] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isImageViewerVisible, setImageViewerVisible] = useState(false);
  const screenWidth = Dimensions.get('window').width;
  const [selectionMode, setSelectionMode] = useState(false);
  const [selectedImageIndexes, setSelectedImageIndexes] = useState<number[]>([]);
  const [activeReport, setActiveReport] = useState<TechnicianReport | null>(null);
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();

  useEffect(() => {
    if (!user) return;

    const db = getFirestore(app);
    const reportsRef = collection(db, 'reports');
    
    // Query for technician reports only
    // Note: Simplified query to avoid composite index requirements
    const q = query(
      reportsRef,
      where('userId', '==', user.uid)
    );

    const unsubscribe = onSnapshot(q, async (snapshot) => {
      const allReports: any[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        // Get all technician-related reports (session and contact reports)
        if (data.userRole === 'tecnico_prorola') {
          allReports.push({ id: doc.id, ...data });
        }
      });
      
      // Group reports by sessionId like webadmin does
      const reportsBySession: { [key: string]: any[] } = {};
      const reportsWithoutSession: any[] = [];
      
      allReports.forEach((report) => {
        const sessionId = report.sessionId;
        if (sessionId) {
          if (!reportsBySession[sessionId]) {
            reportsBySession[sessionId] = [];
          }
          reportsBySession[sessionId].push(report);
        } else {
          reportsWithoutSession.push(report);
        }
      });
      
      // Process grouped reports and merge images like webadmin
      const finalReports: TechnicianReport[] = [];
      
      // Get contact counts for all sessions in parallel
      const sessionIds = Object.keys(reportsBySession);
      const contactCountPromises = sessionIds.map(async (sessionId) => {
        try {
          const contactEventsRef = collection(db, 'contactEvents');
          const contactQuery = query(
            contactEventsRef,
            where('sessionId', '==', sessionId)
          );
          const contactSnapshot = await getDocs(contactQuery);
          return { sessionId, count: contactSnapshot.size };
        } catch (error) {
          console.error(`Error fetching contact count for session ${sessionId}:`, error);
          return { sessionId, count: 0 };
        }
      });
      
      const contactCounts = await Promise.all(contactCountPromises);
      const contactCountMap = contactCounts.reduce((map, { sessionId, count }) => {
        map[sessionId] = count;
        return map;
      }, {} as { [key: string]: number });
      
      Object.values(reportsBySession).forEach((sessionReports) => {
        let sessionReport: DocumentData | undefined;
        let contactReport: DocumentData | undefined;
        
        // Find session report and contact report
        sessionReports.forEach((report) => {
          const reportType = report.type;
          if (reportType === 'tecnico_monitoring_report' || reportType === 'monitoring_session_summary') {
            sessionReport = report;
          } else if (reportType === 'monitoring_session') {
            contactReport = report;
          }
        });
        
        // Use session report as base, but merge images from contact report
        if (sessionReport) {
          const mergedReport: TechnicianReport = {
            ...sessionReport,
            images: sessionReport.images || [],
            contactEventsCount: contactCountMap[sessionReport.sessionId] || 0
          } as TechnicianReport;
          
          // Merge images from contact report if available
          if (contactReport && Array.isArray((contactReport as any).images)) {
            mergedReport.images = (contactReport as any).images;
          }
          
          finalReports.push(mergedReport);
        } else if (contactReport) {
          // If only contact report exists, use it
          const contactOnlyReport = {
            ...contactReport,
            contactEventsCount: contactCountMap[contactReport.sessionId] || 0
          } as TechnicianReport;
          finalReports.push(contactOnlyReport);
        }
      });
      
      // Add reports without sessionId (regular observations)
      reportsWithoutSession.forEach((report) => {
        if (report.type === 'tecnico_monitoring_report' || report.type === 'monitoring_session_summary') {
          const reportWithContactCount = {
            ...report,
            contactEventsCount: 0 // These don't have sessions, so no contact events
          } as TechnicianReport;
          finalReports.push(reportWithContactCount);
        }
      });
      
      // Sort by createdAt descending (client-side to avoid index requirements)
      finalReports.sort((a, b) => {
        const aTime = (a.createdAt && typeof a.createdAt.toMillis === 'function') ? a.createdAt.toMillis() : 0;
        const bTime = (b.createdAt && typeof b.createdAt.toMillis === 'function') ? b.createdAt.toMillis() : 0;
        return bTime - aTime; // Descending order (newest first)
      });
      
      setReports(finalReports);
      setLoading(false);
      setRefreshing(false);
    }, (error) => {
      // Only log errors that aren't related to being offline
      if (error.code !== 'unavailable' && !error.message.includes('client is offline')) {
        console.error('Error fetching technician reports:', error);
      }
      setLoading(false);
      setRefreshing(false);
    });

    return () => unsubscribe();
  }, [user]);

  // Map-related useEffects removed - handled by TechnicianMapScreen component

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Refresh will happen automatically through the snapshot listener
    setRefreshing(false);
  }, []);

  const formatDate = (timestamp: Timestamp | null | undefined) => {
    if (!timestamp || typeof timestamp.toDate !== 'function') {
      return 'Data não disponível';
    }
    try {
      const date = timestamp.toDate();
      return date.toLocaleDateString('pt-PT', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'Data inválida';
    }
  };

  const formatDuration = (durationInSeconds?: number): string => {
    if (!durationInSeconds) return 'N/A';
    
    const hours = Math.floor(durationInSeconds / 3600);
    const minutes = Math.floor((durationInSeconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}min`;
    }
    return `${minutes}min`;
  };

  const getProtocolDisplayName = (protocol?: string): string => {
    if (!protocol) return 'N/A';
    return pt.protocolNames[protocol as keyof typeof pt.protocolNames] || protocol;
  };

  const getReportTypeDisplayName = (type: string): string => {
    switch (type) {
      case 'tecnico_monitoring_report':
        return pt.technicalReport;
      case 'monitoring_session_summary':
        return pt.sessionReport;
      default:
        return type;
    }
  };

  const getDeviceDisplayName = (deviceInfo: { platform: string; model?: string; os?: string }): string => {
    const platform = deviceInfo.platform?.toLowerCase();
    if (platform === 'android') {
      return pt.deviceTypes.android;
    } else if (platform === 'ios') {
      return pt.deviceTypes.ios;
    }
    return pt.deviceTypes.unknown;
  };

  const getDeviceIcon = (platform: string): string => {
    const platformLower = platform?.toLowerCase();
    if (platformLower === 'android') {
      return 'android';
    } else if (platformLower === 'ios') {
      return 'apple';
    }
    return 'mobile-alt';
  };

  const formatContactCount = (count?: number): string => {
    if (count === undefined || count === 0) return '0';
    return count.toString();
  };

  const getLocationName = (location?: { latitude: number; longitude: number }): string => {
    if (!location) return 'Localização desconhecida';
    
    // Simply show GPS coordinates - clean and consistent
    return `${location.latitude.toFixed(3)}, ${location.longitude.toFixed(3)}`;
  };

  const handleReportPress = (report: TechnicianReport) => {
    Alert.alert(
      pt.reportOptions,
      undefined,
      [
        {
          text: pt.view,
          onPress: () => {
            setSelectedReport(report);
            setMapModalVisible(true);
          },
        },
        {
          text: pt.delete,
          style: 'destructive',
          onPress: () => handleDelete(report),
        },
        {
          text: pt.cancel,
          style: 'cancel',
        },
      ]
    );
  };

  const handleDelete = async (report: TechnicianReport) => {
    showAlert({
      type: 'warning',
      message: pt.confirmDelete,
      onConfirm: async () => {
        try {
          const reportRef = doc(db, 'reports', report.id);
          
          // Delete images from storage if they exist
          if (report.images && report.images.length > 0) {
            for (const imageUrl of report.images) {
              try {
                // Skip local file paths - they don't need to be deleted from Firebase Storage
                if (imageUrl.startsWith('file://')) {
                  console.log('Skipping local file path:', imageUrl);
                  continue;
                }
                
                // Only process Firebase Storage URLs
                const matches = imageUrl.match(/o\/(.*?)\?/);
                if (matches && matches[1]) {
                  // Decode the URL-encoded path
                  const decodedPath = decodeURIComponent(matches[1]);
                  const imageRef = storageRef(storage, decodedPath);
                  await deleteObject(imageRef);
                  console.log('Deleted Firebase Storage image:', decodedPath);
                } else {
                  console.log('Skipping non-Firebase URL:', imageUrl);
                }
              } catch (deleteError) {
                console.error('Error deleting image:', deleteError, imageUrl);
                // Continue with other images even if one fails
              }
            }
          }

          // Delete the report document
          await deleteDoc(reportRef);
          
          // Immediately update local state to remove the report from the list
          // This provides instant feedback while Firestore listener updates
          setReports(prevReports => prevReports.filter(r => r.id !== report.id));
          
          showAlert({
            type: 'success',
            message: pt.deleteSuccess
          });
        } catch (error) {
          console.error('Error deleting report:', error);
          showAlert({
            type: 'error',
            message: pt.deleteError
          });
        }
      }
    });
  };

  const handleViewImages = (images: string[]) => {
    setSelectedImages(images);
    setImageViewerVisible(true);
  };

  const handleImageLongPress = (report: TechnicianReport, index: number) => {
    setSelectionMode(true);
    setSelectedImageIndexes([index]);
    setActiveReport(report);
  };

  const handleImageSelect = (index: number) => {
    setSelectedImageIndexes(prev => {
      const isSelected = prev.includes(index);
      if (isSelected) {
        return prev.filter(i => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  const handleDeleteSelectedImages = async () => {
    if (selectedImageIndexes.length === 0) {
      showAlert({
        type: 'error',
        message: pt.noImagesSelected
      });
      return;
    }

    showAlert({
      type: 'warning',
      message: pt.confirmDeleteImage,
      onConfirm: async () => {
        try {
          const sortedIndexes = [...selectedImageIndexes].sort((a, b) => b - a);
          const updatedImages = [...(activeReport?.images || [])];
          
          for (const index of sortedIndexes) {
            const imageUrl = activeReport?.images?.[index];
            if (imageUrl) {
              try {
                // Extract the encoded path from Firebase Storage URL
                const matches = imageUrl.match(/o\/(.*?)\?/);
                if (matches && matches[1]) {
                  // Decode the URL-encoded path
                  const decodedPath = decodeURIComponent(matches[1]);
                  const imageRef = storageRef(storage, decodedPath);
                  await deleteObject(imageRef);
                } else {
                  console.error('Could not parse image URL:', imageUrl);
                }
              } catch (deleteError) {
                console.error('Error deleting image from storage:', deleteError);
              }
              
              updatedImages.splice(index, 1);
            }
          }

          const reportRef = doc(db, 'reports', activeReport?.id || '');
          await updateDoc(reportRef, { images: updatedImages });

          setSelectionMode(false);
          setSelectedImageIndexes([]);
          setActiveReport(null);
          
          showAlert({
            type: 'success',
            message: pt.imageDeleteSuccess
          });
        } catch (error) {
          console.error('Error deleting images:', error);
          showAlert({
            type: 'error',
            message: pt.imageDeleteError
          });
        }
      }
    });
  };

  const renderItem = ({ item }: { item: TechnicianReport }) => (
    <View style={styles.reportCard}>
      {/* Header row with protocol and delete button */}
      <View style={styles.headerRow}>
        <View style={styles.protocolBadge}>
          <FontAwesome name="bars" size={12} color="#6b7280" />
          <Text style={styles.protocolBadgeText}>{getProtocolDisplayName(item.protocol)}</Text>
        </View>
        <TouchableOpacity
          style={styles.deleteReportButton}
          onPress={() => handleDelete(item)}>
          <FontAwesome name="trash" size={16} color="#ff3b30" />
        </TouchableOpacity>
      </View>

      {/* Report Name - centered and prominent */}
      <View style={styles.reportNameRow}>
        {item.reportName ? (
          <Text style={styles.reportNameText}>{item.reportName}</Text>
        ) : (
          <Text style={styles.reportNamePlaceholder}>Sem nome</Text>
        )}
      </View>

      {/* Date and Location on same line - clean and simple */}
      <View style={styles.dateLocationRow}>
        <Text style={styles.reportDate}>{formatDate(item.createdAt)}</Text>
        <Text style={styles.locationText}>{getLocationName(item.location)}</Text>
      </View>

      {/* Compact stats row */}
      <View style={styles.compactStatsRow}>
        <View style={styles.compactStatItem}>
          <FontAwesome name="users" size={10} color="#6b7280" />
          <Text style={styles.compactStatValue}>{item.observersCount || 1}</Text>
        </View>
        
        <View style={styles.compactStatItem}>
          <FontAwesome6 name="dove" size={10} color="#6b7280" />
          <Text style={styles.compactStatValue}>{formatContactCount(item.contactEventsCount)}</Text>
        </View>
        
        <View style={styles.compactStatItem}>
          <FontAwesome name="camera" size={10} color="#6b7280" />
          <Text style={styles.compactStatValue}>{item.images?.length || 0}</Text>
        </View>

        {/* Weather as fourth stat item */}
        {item.weather && (
          <View style={styles.compactStatItem}>
            <FontAwesome name="thermometer-half" size={10} color="#6b7280" />
            <Text style={styles.compactStatValue}>{item.weather.temperature}°</Text>
          </View>
        )}
      </View>
      
      {/* Comments - only if present */}
      {item.comment && (
        <Text style={styles.compactComment} numberOfLines={2}>
          {item.comment}
        </Text>
      )}

      {/* Compact map button */}
      <TouchableOpacity
        style={styles.compactMapButton}
        onPress={() => {
          setSelectedReport(item);
          setMapModalVisible(true);
        }}>
        <FontAwesome name="map" size={12} color="#ffffff" />
        <Text style={styles.compactMapButtonText}>Ver no mapa</Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmptyComponent = () => {
    if (loading) return null;
    
    if (!hasInternetConnection()) {
      return (
        <View style={styles.emptyContainer}>
          <View style={styles.noInternetCard}>
            <FontAwesome6 
              name="wifi" 
              size={48} 
              color="#ff9500" 
              style={{ opacity: 0.6 }}
            />
            <Text style={styles.noInternetText}>
              {pt.noReportsOffline}
            </Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <View style={styles.modernEmptyCard}>
          <View style={styles.iconContainer}>
            <FontAwesome 
              name="clipboard" 
              size={32} 
              color="#0996a8" 
            />
          </View>
          <View style={styles.titleContainer}>
            <FontAwesome6 
              name="circle-info" 
              size={18} 
              color="#7f8c8d" 
              style={{ marginRight: 8, marginTop: 0 }}
            />
            <Text style={styles.modernTitle}>{pt.noTechnicalReports}</Text>
          </View>
          <Text style={styles.modernSubtitle}>
            {pt.technicalReportsWillAppear}
          </Text>
          
          <View style={styles.infoSection}>
            <Text style={styles.infoTitle}>{pt.howToCreateTechnicalReport}</Text>
            <View style={styles.infoItem}>
              <FontAwesome6 name="arrow-right" size={14} color="#0996a8" />
              <Text style={styles.infoText}>{pt.goToLocationPage}</Text>
            </View>
            <View style={styles.infoItem}>
              <FontAwesome6 name="clipboard-check" size={14} color="#0996a8" />
              <Text style={styles.infoText}>{pt.selectMonitoringProtocol}</Text>
            </View>
            <View style={styles.infoItem}>
              <FontAwesome6 name="cloud-sun" size={14} color="#0996a8" />
              <Text style={styles.infoText}>{pt.setWeatherConditions}</Text>
            </View>
            <View style={styles.infoItem}>
              <FontAwesome6 name="play" size={14} color="#0996a8" />
              <Text style={styles.infoText}>{pt.startMonitoringSession}</Text>
            </View>
            <View style={styles.infoItem}>
              <FontAwesome6 name="dove" size={14} color="#0996a8" />
              <Text style={styles.infoText}>{pt.recordContactEvents}</Text>
            </View>
            <View style={styles.infoItem}>
              <FontAwesome6 name="cloud-arrow-up" size={14} color="#0996a8" />
              <Text style={styles.infoText}>{pt.endSessionAndUpload}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  if (Platform.OS === 'web') {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>
          O mapa não está disponível na versão web.
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#0996a8" />
        <Text style={styles.loadingText}>
          {pt.loading}
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>
          {error}
        </Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setLoading(true);
            setError(null);
          }}>
          <Text style={styles.retryButtonText}>{pt.retry}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <FlatList
        data={reports || []}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={(!reports || reports.length === 0) ? styles.emptyContainer : styles.listContainer}
        style={styles.list}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#0996a8']}
            tintColor="#0996a8"
          />
        }
        ListEmptyComponent={renderEmptyComponent}
      />

      <TechnicianMapScreen
        visible={isMapModalVisible}
        report={selectedReport}
        onClose={() => setMapModalVisible(false)}
      />

      <Modal
        visible={isImageViewerVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setImageViewerVisible(false)}
      >
        <View style={styles.imageViewerContainer}>
          <View style={styles.imageViewerHeader}>
            <TouchableOpacity
              style={styles.imageViewerCloseButton}
              onPress={() => setImageViewerVisible(false)}
            >
              <Text style={styles.closeButtonText}>{pt.closeImages}</Text>
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.imageViewerScrollView}>
            {selectedImages.map((imageUrl, index) => (
              <LoadingImage
                key={index}
                source={{ uri: imageUrl }}
                style={{ ...styles.fullImage, width: screenWidth, height: screenWidth }}
                placeholderIcon="image"
                placeholderIconSize={32}
                containerStyle={{ backgroundColor: '#000' }}
              />
            ))}
          </ScrollView>
        </View>
      </Modal>

      <CustomAlert
        visible={isVisible}
        type={config.type}
        message={config.message}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    marginTop: -25,
  },
  list: {
    backgroundColor: '#ffffff',
  },
  listContainer: {
    padding: 15,
    marginTop: 15,
    paddingBottom: 100,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  noReportsText: {
    fontSize: 16,
    color: '#666',
  },
  reportCard: {
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    elevation: 3,
    shadowColor: '#0996a8',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    borderWidth: 0,
    borderLeftWidth: 4,
    borderLeftColor: '#0996a8',
    borderRightWidth: 4,
    borderRightColor: '#0996a8',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    position: 'relative',
  },
  protocolBadge: {
    flexDirection: 'row',
    backgroundColor: '#f8fafc',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    alignItems: 'center',
    gap: 6,
  },
  protocolBadgeText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '600',
  },
  deleteReportButton: {
    backgroundColor: '#fff',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    position: 'absolute',
    right: 0,
  },
  dateLocationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  reportNameRow: {
    alignItems: 'center',
    marginBottom: 10,
  },
  reportNameText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0996a8',
    textAlign: 'center',
  },
  reportNamePlaceholder: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#9CA3AF',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  reportDate: {
    fontSize: 11,
    color: '#6b7280',
    fontWeight: '500',
  },
  locationText: {
    fontSize: 11,
    color: '#0996a8',
    fontWeight: '600',
  },

  compactStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8fffe',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0f2f1',
  },
  compactStatItem: {
    alignItems: 'center',
    flex: 1,
    gap: 1,
  },
  compactStatValue: {
    fontSize: 13,
    fontWeight: '700',
    color: '#0996a8',
    marginTop: 1,
  },
  compactComment: {
    fontSize: 11,
    color: '#6b7280',
    fontStyle: 'italic',
    marginBottom: 8,
    lineHeight: 15,
  },
  compactMapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0996a8',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 0,
    elevation: 2,
    shadowColor: '#0996a8',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  compactMapButtonText: {
    color: '#ffffff',
    marginLeft: 4,
    fontSize: 11,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
    color: '#666',
  },
  retryButton: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Map-related styles moved to TechnicianMapScreen component
  emptyContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingTop: 20,
    paddingBottom: 100,
    paddingHorizontal: 16,
    minHeight: 300,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationLabel: {
    fontSize: 14,
    color: '#666',
  },

  thumbnailContainer: {
    marginTop: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  thumbnailWrapper: {
    width: '31%',
    aspectRatio: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  selectedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 150, 136, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkIcon: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectionControls: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  selectedCount: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  selectionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  selectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    minWidth: 100,
  },
  selectionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  cancelButton: {
    backgroundColor: '#757575',
  },
  deleteButton: {
    backgroundColor: '#ff3b30',
  },
  disabledButton: {
    opacity: 0.5,
  },
  imageViewerContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  imageViewerHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
    padding: 20,
    paddingTop: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  imageViewerCloseButton: {
    alignSelf: 'flex-end',
    backgroundColor: '#757575',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  imageViewerScrollView: {
    flex: 1,
    marginTop: 80,
  },
  fullImage: {
    marginVertical: 10,
  },
  reportField: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    marginRight: 8,
    flexShrink: 0,
    minWidth: 80,
  },
  fieldValue: {
    fontSize: 14,
    color: '#666',
    flex: 1,
    flexWrap: 'wrap',
  },
  modernEmptyCard: {
    backgroundColor: '#f8fffe',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    marginHorizontal: 16,
    borderWidth: 1,
    borderColor: '#e8f4f3',
    width: '100%',
    minHeight: 240,
  },
  iconContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 14,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 2,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'center',
  },
  modernTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#5a6c7d',
    marginTop: 16,
    marginBottom: 6,
    textAlign: 'center',
  },
  modernSubtitle: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'left',
    lineHeight: 15,
    marginTop: 2,
  },
  infoSection: {
    marginTop: 20,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#7f8c8d',
    marginBottom: 12,
    textAlign: 'center',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 13,
    color: '#7f8c8d',
    marginLeft: 10,
  },
  noInternetCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#ff9500',
    marginHorizontal: 20,
  },
  noInternetText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginVertical: 16,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
}); 