import { Redirect } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { View, ActivityIndicator } from 'react-native';
import { useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Index() {
  const { isAuthenticated, isLoading } = useAuth();
  const [hasActiveMonitoring, setHasActiveMonitoring] = useState<boolean | null>(null);

  useEffect(() => {
    const checkActiveMonitoring = async () => {
      try {
        if (isAuthenticated && !isLoading) {
          const activeSession = await AsyncStorage.getItem('activeMonitoringSession');
          setHasActiveMonitoring(!!activeSession);
        } else {
          setHasActiveMonitoring(false);
        }
      } catch (error) {
        console.error('Error checking active monitoring:', error);
        setHasActiveMonitoring(false);
      }
    };

    checkActiveMonitoring();
  }, [isAuthenticated, isLoading]);

  if (isLoading || hasActiveMonitoring === null) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
        <ActivityIndicator size="large" color="#0996a8" />
      </View>
    );
  }

  // If there's an active monitoring session, redirect to report screen
  if (isAuthenticated && hasActiveMonitoring) {
    return <Redirect href="/report" />;
  }

  return <Redirect href={isAuthenticated ? '/(tabs)' : '/auth/login'} />;
} 