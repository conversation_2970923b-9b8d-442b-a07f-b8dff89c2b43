import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import SafeSystemBars from '@/components/SafeSystemBars';
import { View } from 'react-native';

export default function AuthLayout() {
  return (
    <>
      <SafeSystemBars 
        style="light" 
        translucent={true} 
        backgroundColor="#0996a8"
        navigationBarColor="#0996a8"
      />
      <View style={{ flex: 1, backgroundColor: '#fff' }}>
        <Stack
          screenOptions={{
            headerShown: false,
            contentStyle: {
              backgroundColor: '#fff',
            },
          }}>
          <Stack.Screen name="login" />
          <Stack.Screen name="register" />
          <Stack.Screen name="register-type" />
          <Stack.Screen name="verify-email" />
        </Stack>
      </View>
    </>
  );
} 