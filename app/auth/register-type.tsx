import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Platform, ScrollView, KeyboardAvoidingView, Linking } from 'react-native';
import { Text } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import { useNetwork } from '@/contexts/NetworkContext';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import CustomAlert from '@/components/CustomAlert';
import TechnicianRegistrationAlert from '@/components/TechnicianRegistrationAlert';
import HuntingZoneRegistrationAlert from '@/components/HuntingZoneRegistrationAlert';
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';

// Portuguese translations
const pt = {
  howToRegister: 'Como pretende participar no ProROLA?',
  collaborator: 'Colaborador',
  collaboratorDescription: 'Para voluntários, participantes não técnicos',
  technicians: 'Técnico ProROLA',
  techniciansDescription: 'Exclusivo para técnicos credenciados pelo projeto',
  huntingZone: 'Zona de Caça',
  huntingZoneDescription: 'Para gestores de zonas de caça envolvidas no projeto',
  comingSoon: 'Em Breve',
  confirmSelectionTitle: 'Confirmar Seleção',
  confirmSelectionMessage: 'Deseja registar-se como {type}?',
  huntingZoneRegistrationTitle: 'Registo de Zona de Caça',
  huntingZoneRegistrationMessage: 'Para se registar como Gestor de Zona de Caça, deve primeiro registar a sua zona no sistema web.\n\nApós o registo web, poderá iniciar sessão nesta aplicação.',
  registerWebsite: 'Registar no Website',
  haveAccount: 'Já tem uma conta?',
  login: 'Entrar',
  noInternetRegister: 'Não é possível registar sem ligação à Internet',
  offlineMode: 'Modo Offline',
  offlineMessage: 'O registo requer ligação à Internet.',
};

export default function RegisterTypeScreen() {
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const { hasInternetConnection } = useNetwork();
  const [isLoading, setIsLoading] = useState(false);
  const [showTechnicianAlert, setShowTechnicianAlert] = useState(false);
  const [showHuntingZoneAlert, setShowHuntingZoneAlert] = useState(false);

  const handleTechnicianConfirm = () => {
    setShowTechnicianAlert(false);
    console.log('User confirmed registration as: Técnico ProROLA');
    router.push({
      pathname: '/auth/register',
      params: { registrationType: pt.technicians }
    });
  };

  const handleTechnicianCancel = () => {
    setShowTechnicianAlert(false);
  };

  const handleHuntingZoneCancel = () => {
    setShowHuntingZoneAlert(false);
  };

  const handleRegistrationTypeSelection = (type: string) => {
    if (!hasInternetConnection()) {
      showAlert({
        type: 'warning',
        message: pt.noInternetRegister
      });
      return;
    }

    // Special handling for Técnico ProROLA registration with approval warning
    if (type === pt.technicians) {
      setShowTechnicianAlert(true);
    } 
    // Special handling for Zona de Caça - redirect to web registration
    else if (type === pt.huntingZone) {
      setShowHuntingZoneAlert(true);
    } else {
      showAlert({
        type: 'info',
        title: pt.confirmSelectionTitle,
        message: pt.confirmSelectionMessage.replace('{type}', `\n${type}`),
        onConfirm: () => {
          console.log(`User confirmed registration as: ${type}`);
          router.push({
            pathname: '/auth/register',
            params: { registrationType: type }
          });
        },
      });
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.containerKAV}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
      <View style={styles.mainContainer}>
        <View style={styles.header}>
          <SafeAreaView edges={['top']} style={styles.headerContent}>
            <Image
              source={require('../../assets/images/header-logo.png')}
              style={styles.logo}
            />
          </SafeAreaView>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <View style={styles.formContainer}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>{pt.howToRegister}</Text>
              <NetworkStatusIndicator />
            </View>

            {!hasInternetConnection() && (
              <View style={styles.offlineCard}>
                <FontAwesome6 
                  name="wifi" 
                  size={24} 
                  color="#ff9500" 
                  style={{ opacity: 0.8 }}
                />
                <Text style={styles.offlineModeText}>{pt.offlineMode}</Text>
                <Text style={styles.offlineMessageText}>{pt.offlineMessage}</Text>
              </View>
            )}

            {hasInternetConnection() ? (
              <>
                <TouchableOpacity style={styles.button} onPress={() => handleRegistrationTypeSelection(pt.collaborator)}>
                  <Text style={styles.buttonTitle}>{pt.collaborator}</Text>
                  <Text style={styles.buttonDescription}>{pt.collaboratorDescription}</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.button} onPress={() => handleRegistrationTypeSelection(pt.technicians)}>
                  <Text style={styles.buttonTitle}>{pt.technicians}</Text>
                  <Text style={styles.buttonDescription}>{pt.techniciansDescription}</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.button} onPress={() => handleRegistrationTypeSelection(pt.huntingZone)}>
                  <Text style={styles.buttonTitle}>{pt.huntingZone}</Text>
                  <Text style={styles.buttonDescription}>{pt.huntingZoneDescription}</Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <View style={[styles.button, styles.disabledButton]}>
                  <Text style={[styles.buttonTitle, styles.disabledButtonText]}>{pt.collaborator}</Text>
                  <Text style={[styles.buttonDescription, styles.disabledButtonText]}>{pt.collaboratorDescription}</Text>
                </View>

                <View style={[styles.button, styles.disabledButton]}>
                  <Text style={[styles.buttonTitle, styles.disabledButtonText]}>{pt.technicians}</Text>
                  <Text style={[styles.buttonDescription, styles.disabledButtonText]}>{pt.techniciansDescription}</Text>
                </View>

                <View style={[styles.button, styles.disabledButton]}>
                  <Text style={[styles.buttonTitle, styles.disabledButtonText]}>{pt.huntingZone}</Text>
                  <Text style={[styles.buttonDescription, styles.disabledButtonText]}>{pt.huntingZoneDescription}</Text>
                </View>
              </>
            )}

            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>{pt.haveAccount}</Text>
              <TouchableOpacity
                style={styles.loginButton}
                onPress={() => router.push('/auth/login')}
                disabled={isLoading}>
                <FontAwesome name="sign-in" size={20} color="#FFFFFF" style={styles.buttonIcon} />
                <Text style={styles.loginButtonText}>{pt.login}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>

        <SafeAreaView edges={['bottom']} style={styles.footer}>
          <Text style={styles.copyrightText}>
            © {new Date().getFullYear()} Todos os direitos reservados ICNF
          </Text>
        </SafeAreaView>
      </View>

      <CustomAlert
        visible={isVisible}
        type={config.type as 'success' | 'error' | 'warning' | 'info'}
        title={config.title}
        message={config.message || ''}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />

      <TechnicianRegistrationAlert
        visible={showTechnicianAlert}
        onClose={handleTechnicianCancel}
        onConfirm={handleTechnicianConfirm}
      />

      <HuntingZoneRegistrationAlert
        visible={showHuntingZoneAlert}
        onClose={handleHuntingZoneCancel}
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  containerKAV: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#0996a8',
    height: 80,
    zIndex: 2,
    elevation: 2,
  },
  headerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 20,
  },
  logo: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
    top: 30,
    zIndex: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 50,
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 30,
    paddingBottom: 20,
    marginTop: 50,
    justifyContent: 'space-between',
  },
  titleContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#0996a8',
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 0,
    width: '100%',
    maxWidth: 350,
  },
  buttonTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 5,
  },
  buttonDescription: {
    fontSize: 11,
    color: '#FFFFFF',
    textAlign: 'center',
  },
  footer: {
    backgroundColor: '#0996a8',
    padding: 10,
    width: '100%',
  },
  copyrightText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
  },
  loginContainer: {
    marginTop: 0,
    alignItems: 'center',
    gap: 10,
  },
  loginText: {
    color: '#666',
    fontSize: 14,
  },
  loginButton: {
    flexDirection: 'row',
    backgroundColor: '#0996a8',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    alignItems: 'center',
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  buttonIcon: {
    marginRight: 10,
  },
  offlineCard: {
    backgroundColor: '#fef7f0',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#ff9500',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  offlineModeText: {
    color: '#e65100',
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    marginBottom: 8,
  },
  offlineMessageText: {
    color: '#bf360c',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 18,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  disabledButtonText: {
    color: '#666',
  },
  comingSoonButton: {
    opacity: 0.7,
  },
  comingSoonBadge: {
    backgroundColor: '#ff9500',
    paddingHorizontal: 6,
    paddingVertical: 1,
    borderRadius: 8,
    alignSelf: 'center',
    marginBottom: 3,
  },
  comingSoonText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
}); 