import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, TextInput, Alert, ActivityIndicator, Platform, ScrollView, Modal, ViewStyle } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useNetwork } from '@/contexts/NetworkContext';
import { FontAwesome } from '@expo/vector-icons';
import { router } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import { t } from '@/config/translations';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LoadingImage from '../components/LoadingImage';
import PhotoSelectionAlert from '@/components/PhotoSelectionAlert';
import NetworkAwareButton from '@/components/NetworkAwareButton';
import { getAuth } from 'firebase/auth';

const auth = getAuth();

// Portuguese translations
const pt = {
  editProfile: 'Editar Perfil',
  save: 'Guardar',
  cancel: 'Cancelar',
  ok: 'OK',
  logout: 'Terminar Sessão',
  resetPassword: 'Alterar Password',
  deleteAccount: 'Eliminar Conta',
  firstName: 'Nome',
  lastName: 'Apelido',
  email: 'Email',
  memberSince: 'Membro desde',
  required: 'Campo obrigatório',
  confirmDelete: 'Tem certeza que deseja eliminar a sua conta?',
  confirmLogout: 'Tem certeza que deseja terminar a sessão?',
  yes: 'Sim',
  no: 'Não',
  error: 'Erro',
  success: 'Sucesso',
  profileUpdated: 'Perfil atualizado com sucesso.',
  photoOptions: 'Foto de Perfil',
  takePhoto: 'Tirar Foto',
  choosePhoto: 'Escolher da Galeria',
  cancelPhoto: 'Cancelar',
  permissionDenied: 'Permissão negada para acessar a câmera ou galeria.',
  errorUploadingPhoto: 'Erro ao carregar a foto. Tente novamente.',
  deleteAccountTitle: 'Eliminar Conta',
  deleteAccountMessage: 'Para eliminar a sua conta, por favor insira a sua palavra-passe:',
  password: 'Palavra-passe',
  confirm: 'Confirmar',
  changePasswordTitle: 'Alterar Password',
  currentPassword: 'Password Atual',
  newPassword: 'Nova Password',
  confirmNewPassword: 'Confirmar Nova Password',
  passwordChanged: 'Password alterada com sucesso.',
  passwordsDontMatch: 'As passwords não coincidem.',
  passwordTooShort: 'A password deve ter pelo menos 6 caracteres.',
  incorrectPassword: 'Password atual incorreta.',
  accountSettings: 'Configurações da Conta',
  personalInfo: 'Informações Pessoais',
  // Role translations
  roles: {
    administrador: 'Administrador',
    tecnico_prorola: 'Técnico ProROLA',
    colaborador: 'Colaborador',
    zona_de_caca: 'Zona de Caça',
    utilizador_movel: 'Utilizador Móvel',
    gestor_caca: 'Gestor de Zonas de Caça',
  }
};

// Function to get role label
const getRoleLabel = (role: string | null): string => {
  if (!role) return '';
  return pt.roles[role as keyof typeof pt.roles] || role;
};

export default function ProfileScreen() {
  const { user, logout, updateUserProfile, deleteAccount, updatePassword, userRole } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  
  // Debug logging for user role
  useEffect(() => {
    console.log(`📋 ProfileScreen: Current user role: ${userRole}`);
    console.log(`👤 ProfileScreen: Current user:`, { uid: user?.uid, email: user?.email, displayName: user?.displayName });
  }, [userRole, user]);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialSetup, setIsInitialSetup] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [showEditProfileModal, setShowEditProfileModal] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
  });

  // Update form data when user changes
  useEffect(() => {
    if (user?.displayName) {
      // Split by spaces and filter out empty strings to handle multiple spaces
      const nameParts = user.displayName.split(' ').filter(part => part.trim() !== '');
      setFormData({
        firstName: nameParts[0] || '',
        lastName: nameParts.slice(1).join(' ') || '', // Join all remaining parts as last name
      });
    }
  }, [user?.displayName]);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: '',
  });
  const [errors, setErrors] = useState({
    firstName: '',
    lastName: '',
  });
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const [password, setPassword] = useState('');
  const [isPhotoSelectionVisible, setPhotoSelectionVisible] = useState(false);
  const [imageRefreshKey, setImageRefreshKey] = useState(0); // Only refresh when needed
  const [isImageUploading, setIsImageUploading] = useState(false);

  const { hasInternetConnection } = useNetwork();

  useEffect(() => {
    const checkProfileSetup = async () => {
      const needsSetup = await AsyncStorage.getItem('needsProfileSetup');
      if (needsSetup === 'true') {
        setShowEditProfileModal(true);
        setIsInitialSetup(true);
        showAlert({
          type: 'success',
          message: 'Por favor, complete o seu perfil inserindo o seu nome e apelido.',
        });
      }
    };
    checkProfileSetup();
  }, []);

  // Format the date
  const memberSince = user?.metadata.creationTime 
    ? new Date(user.metadata.creationTime).toLocaleDateString('pt-PT', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      })
    : new Date().toLocaleDateString('pt-PT', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });

  const validateForm = () => {
    const newErrors = {
      firstName: '',
      lastName: '',
    };
    let isValid = true;

    if (!formData.firstName.trim()) {
      newErrors.firstName = pt.required;
      isValid = false;
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = pt.required;
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handlePress = (handler: () => void) => {
    if (Platform.OS === 'web') {
      setTimeout(handler, 0);
    } else {
      handler();
    }
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const fullName = `${formData.firstName.trim()} ${formData.lastName.trim()}`;
      await updateUserProfile(fullName);
      setShowEditProfileModal(false);
      await AsyncStorage.removeItem('needsProfileSetup');
      showAlert({
        type: 'success',
        message: t('alerts.success.profileUpdated'),
      });
      
      // Only redirect to tabs during initial setup, not regular editing
      if (isInitialSetup) {
        router.replace('/(tabs)');
      }
      
      // Reset initial setup flag
      setIsInitialSetup(false);
    } catch (error: any) {
      showAlert({
        type: 'error',
        message: error.message || t('alerts.error.generic'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    showAlert({
      type: 'info',
      message: pt.confirmLogout,
      onConfirm: async () => {
        try {
          await logout();
        } catch (error: any) {
          showAlert({
            type: 'error',
            message: error.message || t('alerts.error.generic'),
          });
        }
      }
    });
  };

  const handleResetPassword = () => {
    setShowChangePasswordModal(true);
  };

  const validatePasswordForm = () => {
    if (passwordData.newPassword.length < 6) {
      showAlert({
        type: 'error',
        message: pt.passwordTooShort
      });
      return false;
    }

    if (passwordData.newPassword !== passwordData.confirmNewPassword) {
      showAlert({
        type: 'error',
        message: pt.passwordsDontMatch
      });
      return false;
    }

    return true;
  };

  const handleChangePassword = async () => {
    if (!validatePasswordForm()) return;

    setIsLoading(true);
    try {
      await updatePassword(passwordData.currentPassword, passwordData.newPassword);
      setShowChangePasswordModal(false);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: '',
      });
      showAlert({
        type: 'success',
        message: pt.passwordChanged
      });
    } catch (error: any) {
      showAlert({
        type: 'error',
        message: error.message || 'Ocorreu um erro. Por favor, tente novamente.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAccount = () => {
    showAlert({
      type: 'warning',
      message: pt.confirmDelete,
      onConfirm: () => setShowPasswordModal(true)
    });
  };

  const handleDeleteProfileImage = () => {
    showAlert({
      type: 'warning',
      message: 'Tem certeza que deseja remover a sua foto de perfil?',
      onConfirm: async () => {
        setIsLoading(true);
        try {
          await updateUserProfile(undefined, null); // null removes the photo
          
          // Force image refresh by updating key
          setImageRefreshKey((prev: number) => prev + 1);
          
          // Force a small delay to ensure state updates
          await new Promise(resolve => setTimeout(resolve, 500));
          
          showAlert({
            type: 'success',
            message: 'Foto de perfil removida com sucesso.',
          });
        } catch (error: any) {
          showAlert({
            type: 'error',
            message: 'Erro ao remover a foto. Tente novamente.',
          });
        } finally {
          setIsLoading(false);
        }
      }
    });
  };

  const handleConfirmDelete = async () => {
    try {
      await deleteAccount(password);
      setShowPasswordModal(false);
      router.replace('/auth/login');
    } catch (error: any) {
      showAlert({
        type: 'error',
        message: error.message === 'auth/requires-recent-login' 
          ? 'Para sua segurança, por favor termine sessão e volte a entrar antes de eliminar a sua conta.'
          : error.message || t('alerts.error.generic')
      });
    }
  };

  const handleImagePress = () => {
    setPhotoSelectionVisible(true);
  };

  const pickImage = async (source: 'camera' | 'gallery') => {
    try {
      let permissionResult;
      if (source === 'camera') {
        permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      } else {
        permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      }

      if (!permissionResult.granted) {
        Alert.alert(pt.error, pt.permissionDenied);
        return;
      }

      const result = await (source === 'camera'
        ? ImagePicker.launchCameraAsync({
            allowsEditing: true,
            aspect: [1, 1],
            quality: 0.5,
          })
        : ImagePicker.launchImageLibraryAsync({
            allowsEditing: true,
            aspect: [1, 1],
            quality: 0.5,
          }));

      if (!result.canceled && result.assets[0].uri) {
        setIsImageUploading(true);
        try {
          await updateUserProfile(undefined, result.assets[0].uri);
          
          // Force image refresh after successful upload
          setImageRefreshKey((prev: number) => prev + 1);
          
          showAlert({
            type: 'success',
            message: t('alerts.success.photoUpdated'),
          });
        } catch (error: any) {
          showAlert({
            type: 'error',
            message: t('alerts.error.uploadError'),
          });
        } finally {
          setIsImageUploading(false);
        }
      }
    } catch (error) {
      showAlert({
        type: 'error',
        message: t('alerts.error.uploadError'),
      });
    }
  };



  // Get the image source, ensuring it's never null
  const getImageSource = () => {
    // Only show image if online and have photoURL that's not null/empty
    if (hasInternetConnection() && user?.photoURL && user.photoURL.trim() !== '' && user.photoURL !== 'null') {
      // Only add cache busting when image was actually refreshed
      const separator = user.photoURL.includes('?') ? '&' : '?';
      return { uri: `${user.photoURL}${separator}refresh=${imageRefreshKey}` };
    }
    return undefined;
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.contentContainer}>
        {/* Header Section */}
        <View style={styles.headerSection}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              {(isLoading || isImageUploading) ? (
                <View style={styles.avatarPlaceholder}>
                  <ActivityIndicator size="large" color="#00b4a6" />
                </View>
              ) : getImageSource() ? (
                <LoadingImage 
                  key={`profile-image-${imageRefreshKey}`}
                  source={getImageSource()} 
                  style={styles.avatar}
                  containerStyle={styles.avatarImageContainer}
                  placeholderIcon="user"
                  placeholderIconSize={40}
                  placeholderIconColor="#00b4a6"
                />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <FontAwesome 
                    name={hasInternetConnection() ? "user" : "wifi"} 
                    size={50} 
                    color={hasInternetConnection() ? "#00b4a6" : "#ccc"} 
                  />
                </View>
              )}
            </View>
            
            {!isEditing && (
              <View style={styles.userInfo}>
                <Text style={styles.userName}>
                  {user?.displayName || 'Utilizador'}
                </Text>
                
                {/* User Role Display */}
                {userRole && (
                  <View style={styles.roleBadge}>
                    <Text style={styles.roleText}>
                      {getRoleLabel(userRole)}
                    </Text>
                  </View>
                )}
              </View>
            )}
          </View>
        </View>

        {/* Personal Information Card */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>{pt.personalInfo}</Text>
          
          <View style={styles.infoList}>
            <View style={styles.infoItem}>
              <FontAwesome name="envelope" size={18} color="#00b4a6" style={styles.infoIcon} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>{pt.email}</Text>
                <Text style={styles.infoValue}>{user?.email || 'Não disponível'}</Text>
              </View>
            </View>
            
            <View style={styles.infoItem}>
              <FontAwesome name="calendar" size={18} color="#00b4a6" style={styles.infoIcon} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>{pt.memberSince}</Text>
                <Text style={styles.infoValue}>{memberSince}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Account Settings Card */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>{pt.accountSettings}</Text>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setShowEditProfileModal(true)}
            >
              <FontAwesome name="edit" size={20} color="#00b4a6" style={styles.actionButtonIcon} />
              <Text style={styles.actionButtonText}>{pt.editProfile}</Text>
              <FontAwesome name="chevron-right" size={14} color="#a4b0be" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleResetPassword}
            >
              <FontAwesome name="key" size={20} color="#00b4a6" style={styles.actionButtonIcon} />
              <Text style={styles.actionButtonText}>{pt.resetPassword}</Text>
              <FontAwesome name="chevron-right" size={14} color="#a4b0be" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleLogout}
            >
              <FontAwesome name="sign-out" size={20} color="#ffa502" style={styles.actionButtonIcon} />
              <Text style={[styles.actionButtonText, { color: '#ffa502' }]}>{pt.logout}</Text>
              <FontAwesome name="chevron-right" size={14} color="#a4b0be" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.dangerButton]}
              onPress={handleDeleteAccount}
            >
              <FontAwesome name="trash" size={20} color="#ff4757" style={styles.actionButtonIcon} />
              <Text style={[styles.actionButtonText, { color: '#ff4757' }]}>{pt.deleteAccount}</Text>
              <FontAwesome name="chevron-right" size={14} color="#a4b0be" />
            </TouchableOpacity>
          </View>
        </View>

        <CustomAlert
          visible={isVisible}
          type={config.type}
          message={config.message}
          onClose={hideAlert}
          onConfirm={config.onConfirm}
        />
        
        <Modal
          visible={showPasswordModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowPasswordModal(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalTitleContainer}>
                <FontAwesome name="trash" size={20} color="#e74c3c" />
                <Text style={styles.modalTitle}>{pt.deleteAccountTitle}</Text>
              </View>
              <Text style={styles.modalMessage}>{pt.deleteAccountMessage}</Text>
              <TextInput
                style={styles.passwordInput}
                placeholder={pt.password}
                placeholderTextColor="#a4b0be"
                secureTextEntry
                value={password}
                onChangeText={setPassword}
              />
              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => {
                    setShowPasswordModal(false);
                    setPassword('');
                  }}
                >
                  <FontAwesome name="times" size={18} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.modalButtonText}>{pt.cancel}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, styles.confirmButton]}
                  onPress={handleConfirmDelete}
                >
                  <FontAwesome name="check" size={18} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.modalButtonText}>{pt.confirm}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        
        <Modal
          visible={showChangePasswordModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowChangePasswordModal(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalTitleContainer}>
                <FontAwesome name="key" size={20} color="#0996a8" />
                <Text style={styles.modalTitle}>{pt.changePasswordTitle}</Text>
              </View>
              
              <TextInput
                style={styles.passwordInput}
                placeholder={pt.currentPassword}
                placeholderTextColor="#a4b0be"
                secureTextEntry
                value={passwordData.currentPassword}
                onChangeText={(text) => setPasswordData(prev => ({ ...prev, currentPassword: text }))}
              />

              <TextInput
                style={styles.passwordInput}
                placeholder={pt.newPassword}
                placeholderTextColor="#a4b0be"
                secureTextEntry
                value={passwordData.newPassword}
                onChangeText={(text) => setPasswordData(prev => ({ ...prev, newPassword: text }))}
              />

              <TextInput
                style={styles.passwordInput}
                placeholder={pt.confirmNewPassword}
                placeholderTextColor="#a4b0be"
                secureTextEntry
                value={passwordData.confirmNewPassword}
                onChangeText={(text) => setPasswordData(prev => ({ ...prev, confirmNewPassword: text }))}
              />

              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => {
                    setShowChangePasswordModal(false);
                    setPasswordData({
                      currentPassword: '',
                      newPassword: '',
                      confirmNewPassword: '',
                    });
                  }}
                  disabled={isLoading}
                >
                  <FontAwesome name="times" size={18} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.modalButtonText}>{pt.cancel}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, styles.primaryButton]}
                  onPress={handleChangePassword}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <ActivityIndicator color="#FFFFFF" />
                  ) : (
                    <>
                      <FontAwesome name="check" size={18} color="#FFFFFF" style={styles.buttonIcon} />
                      <Text style={styles.modalButtonText}>{pt.confirm}</Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        
        <Modal
          visible={showEditProfileModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowEditProfileModal(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalTitleContainer}>
                <FontAwesome name="edit" size={20} color="#0996a8" />
                <Text style={styles.modalTitle}>{pt.editProfile}</Text>
              </View>
              
                            {/* Profile Image Section */}
              <View style={styles.modalAvatarSection}>
                <View style={styles.modalAvatarContainer}>
                  <TouchableOpacity 
                    onPress={() => handlePress(handleImagePress)}
                    disabled={isLoading || isImageUploading}>
                    {(isLoading || isImageUploading) ? (
                      <View style={styles.modalAvatarPlaceholder}>
                        <ActivityIndicator size="large" color="#0996a8" />
                      </View>
                    ) : getImageSource() ? (
                      <LoadingImage 
                        key={`modal-image-${imageRefreshKey}`}
                        source={getImageSource()} 
                        style={styles.modalAvatar}
                        containerStyle={styles.modalAvatarImageContainer}
                        placeholderIcon="user"
                        placeholderIconSize={40}
                        placeholderIconColor="#0996a8"
                      />
                    ) : (
                      <View style={styles.modalAvatarPlaceholder}>
                        <FontAwesome 
                          name={hasInternetConnection() ? "user" : "wifi"} 
                          size={40} 
                          color={hasInternetConnection() ? "#0996a8" : "#ccc"} 
                        />
                        <View style={styles.modalCameraOverlay}>
                          <FontAwesome name="camera" size={16} color="#fff" />
                        </View>
                      </View>
                    )}
                  </TouchableOpacity>
                  
                  {/* Delete button - only show if user has an image and is online */}
                  {getImageSource() && hasInternetConnection() && (
                    <TouchableOpacity 
                      style={styles.deleteImageButton}
                      onPress={handleDeleteProfileImage}
                      disabled={isLoading}>
                      <FontAwesome name="times" size={12} color="#fff" />
                    </TouchableOpacity>
                  )}
                </View>
                <Text style={styles.modalAvatarText}>
                  {getImageSource() ? 'Tocar para alterar foto' : 'Tocar para adicionar foto'}
                </Text>
              </View>
              
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>{pt.firstName}</Text>
                <TextInput
                  style={[
                    styles.passwordInput,
                    { borderColor: errors.firstName ? '#ff4757' : '#e8ecf0' }
                  ]}
                  placeholder={pt.firstName}
                  placeholderTextColor="#a4b0be"
                  value={formData.firstName}
                  onChangeText={(text) => setFormData({ ...formData, firstName: text })}
                  editable={!isLoading && !isImageUploading}
                />
                {errors.firstName ? <Text style={styles.errorText}>{errors.firstName}</Text> : null}
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>{pt.lastName}</Text>
                <TextInput
                  style={[
                    styles.passwordInput,
                    { borderColor: errors.lastName ? '#ff4757' : '#e8ecf0' }
                  ]}
                  placeholder={pt.lastName}
                  placeholderTextColor="#a4b0be"
                  value={formData.lastName}
                  onChangeText={(text) => setFormData({ ...formData, lastName: text })}
                  editable={!isLoading && !isImageUploading}
                />
                {errors.lastName ? <Text style={styles.errorText}>{errors.lastName}</Text> : null}
              </View>

              <View style={styles.modalButtons}>
                {!isInitialSetup && (
                  <TouchableOpacity
                    style={[styles.modalButton, styles.cancelButton]}
                    onPress={() => {
                      setShowEditProfileModal(false);
                      setErrors({ firstName: '', lastName: '' });
                    }}
                    disabled={isLoading}
                  >
                    <FontAwesome name="times" size={18} color="#FFFFFF" style={styles.buttonIcon} />
                    <Text style={styles.modalButtonText}>{pt.cancel}</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={[styles.modalButton, styles.primaryButton]}
                  onPress={handleSave}
                  disabled={isLoading || isImageUploading}
                >
                  {isLoading ? (
                    <ActivityIndicator color="#FFFFFF" />
                  ) : (
                    <>
                      <FontAwesome name="check" size={18} color="#FFFFFF" style={styles.buttonIcon} />
                      <Text style={styles.modalButtonText}>{pt.save}</Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        
        <PhotoSelectionAlert
          visible={isPhotoSelectionVisible}
          onClose={() => setPhotoSelectionVisible(false)}
          onTakePhoto={() => {
            setPhotoSelectionVisible(false);
            pickImage('camera');
          }}
          onChoosePhoto={() => {
            setPhotoSelectionVisible(false);
            pickImage('gallery');
          }}
        />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 120, // Add extra padding for tab bar
  },
  headerSection: {
    backgroundColor: '#ffffff',
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  profileHeader: {
    alignItems: 'center',
  },
  avatarContainer: {
    marginBottom: 8,
    position: 'relative',
  },
  avatarImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#fff',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.12,
    shadowRadius: 2.5,
    overflow: 'hidden',
  } as ViewStyle,
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.12,
    shadowRadius: 2.5,
    position: 'relative',
    borderWidth: 1,
    borderColor: '#e8ecf0',
  },
  avatar: {
    width: '100%',
    height: '100%',
    borderRadius: 40,
  },
  cameraOverlay: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#0996a8',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  userInfo: {
    alignItems: 'center',
  },
  userName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#0996a8',
    textAlign: 'center',
    marginBottom: 6,
  },
  card: {
    backgroundColor: '#f0f9ff',
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
    elevation: 3,
    shadowColor: '#0996a8',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    borderWidth: 0,
    borderLeftWidth: 4,
    borderLeftColor: '#0996a8',
    borderRightWidth: 4,
    borderRightColor: '#0996a8',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4a5568',
    marginBottom: 8,
    textAlign: 'center',
  },
  editForm: {
    gap: 12,
  },
  inputGroup: {
    gap: 4,
  },
  inputLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6b7280',
    marginBottom: 2,
  },
  input: {
    height: 40,
    borderWidth: 1.5,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 14,
    color: '#4a5568',
    backgroundColor: '#fff',
  },
  errorText: {
    color: '#ff4757',
    fontSize: 11,
    marginTop: 2,
  },
  infoList: {
    gap: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoIcon: {
    marginRight: 12,
    width: 18,
    textAlign: 'center',
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 10,
    color: '#9ca3af',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 1,
  },
  infoValue: {
    fontSize: 14,
    color: '#4a5568',
    fontWeight: '500',
  },
  buttonContainer: {
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f2f6',
  },
  actionButtonIcon: {
    marginRight: 12,
    width: 18,
    textAlign: 'center',
  },
  actionButtonText: {
    flex: 1,
    fontSize: 14,
    color: '#4a5568',
    fontWeight: '500',
  },
  dangerButton: {
    borderBottomWidth: 0,
  },
  roleBadge: {
    backgroundColor: '#0996a8',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderWidth: 1,
    borderColor: '#0996a8',
  },
  roleText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.3,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '100%',
    maxWidth: 360,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 5.84,
  },
  modalTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    gap: 10,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#4a5568',
  },
  modalMessage: {
    fontSize: 14,
    marginBottom: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  passwordInput: {
    height: 44,
    borderWidth: 1.5,
    borderColor: '#e8ecf0',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 14,
    color: '#4a5568',
    backgroundColor: '#fff',
    marginBottom: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10,
    marginTop: 8,
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 100,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  buttonIcon: {
    marginRight: 6,
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
  },
  confirmButton: {
    backgroundColor: '#e74c3c',
  },
  primaryButton: {
    backgroundColor: '#0996a8',
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  modalAvatarSection: {
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 16,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
  },
  modalAvatarContainer: {
    marginBottom: 8,
    position: 'relative',
    alignItems: 'center',
  },
  modalAvatarImageContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#fff',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    overflow: 'hidden',
  } as ViewStyle,
  modalAvatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    position: 'relative',
    borderWidth: 1,
    borderColor: '#e8ecf0',
  },
  modalAvatar: {
    width: '100%',
    height: '100%',
    borderRadius: 50,
  },
  modalCameraOverlay: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    backgroundColor: '#0996a8',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  deleteImageButton: {
    position: 'absolute',
    top: -12,
    right: -12,
    backgroundColor: '#ff4757',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  modalAvatarText: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
}); 