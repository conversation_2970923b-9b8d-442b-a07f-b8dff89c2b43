import React, { useState, useEffect } from 'react';
import { Tabs } from 'expo-router';
import { FontAwesome } from '@expo/vector-icons';
import { Image, View, Text, Dimensions } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import SafeSystemBars from '@/components/SafeSystemBars';
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';
import { useAuth } from '@/contexts/AuthContext';

function TabBarIcon(props: {
  name: React.ComponentProps<typeof FontAwesome>['name'];
  color: string;
}) {
  return <FontAwesome size={24} style={{ marginBottom: 0 }} {...props} />;
}

function HeaderLogo({ isLandscape }: { isLandscape: boolean }) {
  return (
    <View style={{
      position: 'absolute',
      left: 12,
      top: isLandscape ? 30 : 42,
      zIndex: 1,
    }}>
      <Image 
        source={require('../../assets/images/header-logo.png')}
        style={{
          width: isLandscape ? 75 : 90,
          height: isLandscape ? 75 : 90,
          resizeMode: 'contain',
        }}
      />
    </View>
  );
}

function PageTitle({ title }: { title: string }) {
  return (
    <View style={{ 
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#ffffff',
      height: 45,
      paddingTop: 0,
    }}>
      <Text style={{ 
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
      }}>{title}</Text>
    </View>
  );
}

export default function TabLayout() {
  const { userRole } = useAuth();
  const [screenData, setScreenData] = useState(Dimensions.get('window'));
  const isLandscape = screenData.width > screenData.height;
  const insets = useSafeAreaInsets();
  
  // Check if user is a hunting zone manager
  const isGestorZonaCaca = userRole === 'gestor_caca';
  
  // Debug logging
  console.log(`🔍 TabLayout: userRole = ${userRole}, isGestorZonaCaca = ${isGestorZonaCaca}`);
  
  useEffect(() => {
    const onChange = (result: { window: any }) => {
      setScreenData(result.window);
    };
    
    const subscription = Dimensions.addEventListener('change', onChange);
    return () => subscription?.remove();
  }, []);
  
  return (
    <>
      {/* Using SafeSystemBars with navigation bar color matching */}
      <SafeSystemBars 
        style="light" 
        translucent={true} 
        backgroundColor="#0996a8"
        navigationBarColor="#0996a8"
      />
      {/* Navigation bar will match the app theme color */}
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: '#ffffff',
          tabBarInactiveTintColor: '#ffffff',
          tabBarStyle: {
            height: 60 + insets.bottom,
            backgroundColor: '#0996a8',
            borderTopColor: 'transparent',
            paddingBottom: insets.bottom,
            position: 'absolute',
            bottom: 0,
          },
          tabBarLabelStyle: {
            fontSize: isLandscape ? 13 : 12,
            paddingBottom: isLandscape ? 8 : 15,
            marginTop: isLandscape ? 3 : 0,
            textAlign: 'center',
            marginBottom: insets.bottom > 0 ? 0 : 5,
          },
          tabBarItemStyle: {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            height: 65,
            paddingHorizontal: isLandscape ? 2 : 0,
            paddingBottom: insets.bottom > 0 ? insets.bottom / 2 : 0,
          },
          tabBarIconStyle: {
            marginBottom: isLandscape ? 2 : 5,
          },
          headerStyle: {
            backgroundColor: '#0996a8',
            height: 60,
          },
          headerTitleStyle: {
            display: 'none',
          },
          headerShadowVisible: false,
          header: ({ route }) => (
            <View style={{ backgroundColor: '#0996a8' }}>
              <SafeAreaView edges={['top']} style={{ backgroundColor: '#0996a8' }}>
                <View style={{ height: Math.max(isLandscape ? 20 : 40, insets.top > 0 ? 10 : 40) }} />
              </SafeAreaView>
              <HeaderLogo isLandscape={isLandscape} />
              <View style={{ 
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#ffffff',
                height: 45,
                paddingTop: 0,
                position: 'relative',
              }}>
                <Text style={{ 
                  fontSize: 15,
                  color: '#666',
                  fontWeight: '500',
                }}>{route.name === 'index' ? (isGestorZonaCaca ? 'Zonas de Caça' : 'Localização') : 
                      route.name === 'jornadas' ? 'Jornadas de Caça' :
                      route.name === 'reports' ? 'Relatórios' :
                      route.name === 'profile' ? 'Perfil' :
                      route.name === 'weather-conditions' ? 'Condições' :
                      route.name === 'about' ? 'Sobre' :
                      route.name === 'privacy' ? 'Privacidade' : 'Mais'}</Text>
                {(route.name === 'index' || route.name === 'profile' || route.name === 'weather-conditions' || route.name === 'reports' || route.name === 'settings') && (
                  <NetworkStatusIndicator style={{
                    marginTop: 2,
                    transform: [{ scale: 0.8 }],
                  }} />
                )}
              </View>
            </View>
          ),
        }}>
        <Tabs.Screen
          name="index"
          options={{
            title: isGestorZonaCaca ? 'Zonas' : 'Localização',
            tabBarIcon: ({ color }) => <TabBarIcon name={isGestorZonaCaca ? "binoculars" : "map-marker"} color={color} />,
          }}
        />
        <Tabs.Screen
          name="jornadas"
          options={{
            title: 'Jornadas',
            tabBarIcon: ({ color }) => <TabBarIcon name="calendar" color={color} />,
            href: isGestorZonaCaca ? undefined : null, // Only accessible for gestor users
          }}
        />
        <Tabs.Screen
          name="reports"
          options={{
            title: 'Relatórios',
            tabBarIcon: ({ color }) => <TabBarIcon name="list" color={color} />,
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            title: 'Perfil',
            tabBarIcon: ({ color }) => <TabBarIcon name="user" color={color} />,
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            title: 'Mais',
            tabBarIcon: ({ color }) => <TabBarIcon name="ellipsis-h" color={color} />,
          }}
        />
        <Tabs.Screen
          name="technicianReports"
          options={{
            href: null,
            title: 'Relatórios Técnicos',
          }}
        />
        <Tabs.Screen
          name="weather-conditions"
          options={{
            href: null,
            title: 'Condições Meteorológicas',
          }}
        />
        <Tabs.Screen
          name="about"
          options={{
            href: null,
            title: 'Sobre',
          }}
        />
        <Tabs.Screen
          name="privacy"
          options={{
            href: null,
            title: 'Privacidade',
          }}
        />
      </Tabs>
    </>
  );
}
