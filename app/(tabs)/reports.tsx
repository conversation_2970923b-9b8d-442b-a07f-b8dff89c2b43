import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  Modal,
  Platform,
  Image,
  Dimensions,
  ScrollView,
  Pressable,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useNetwork } from '@/contexts/NetworkContext';
import app, { db } from '@/config/firebase';
import {
  collection,
  query,
  where,
  orderBy,
  getDocs,
  Timestamp,
  deleteDoc,
  doc,
  onSnapshot,
  getFirestore,
  DocumentData,
  updateDoc,
} from 'firebase/firestore';
import { ref as storageRef, deleteObject } from 'firebase/storage';
import { storage } from '@/config/firebase';
import MapView, { Marker, MapMarker, PROVIDER_GOOGLE, MapType } from 'react-native-maps';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import LoadingImage from '../components/LoadingImage';
import { t } from '@/config/translations';
import { ReportCircumstances, ReportContactLocation } from '@/types/reports';
import TechnicianReportsScreen from '../components/reports/TechnicianReports';
import GestorReportsScreen from '@/components/reports/GestorReports';

interface Report extends DocumentData {
  id: string;
  userId: string;
  userName: string;
  userEmail?: string;
  userRole?: string;
  type: 'colaborador_report';
  comment?: string;
  createdAt: Timestamp | null;
  images?: string[];
  location: {
    latitude: number;
    longitude: number;
  };
  // New fields for colaborador reports
  circumstances?: ReportCircumstances;
  contactLocation?: ReportContactLocation;
  weather?: {
    temperature: number;
    feelsLike: number;
    description: string;
    humidity: number;
    pressure: number;
    pressureTrend?: 'rising' | 'falling' | 'steady';
    windSpeed: number;
    windDirection: number;
    windGust?: number;
    visibility: number;
    cloudiness: number;
    uvIndex?: number;
    dewPoint?: number;
    icon: string;
    timestamp: number;
    sunrise: number;
    sunset: number;
    moonPhase?: number;
    barometricTrend?: string;
  };
}

const pt = {
  noReports: 'Não existem relatórios',
  noInternetReports: 'Não é possível carregar relatórios sem ligação à Internet',
  hunting: 'Estou a caçar',
  notHunting: 'Não estou a caçar',
  loading: 'A carregar relatórios...',
  date: 'Data',
  location: 'Localização',
  comment: 'Comentário',
  type: 'Tipo',
  error: 'Erro ao carregar relatórios',
  retry: 'Tentar novamente',
  view: 'Ver',
  delete: 'Eliminar',
  cancel: 'Cancelar',
  confirmDelete: 'Tem certeza que deseja eliminar este relatório?',
  yes: 'Sim',
  no: 'Não',
  reportOptions: 'Opções do Relatório',
  deleteSuccess: 'Relatório eliminado com sucesso',
  deleteError: 'Erro ao eliminar relatório',
  success: 'Sucesso',
  huntingStatus: {
    hunting: 'Está a caçar',
    notHunting: 'Não está a caçar'
  },
  viewImages: 'Ver Imagens',
  closeImages: 'Fechar',
  deleteImage: 'Eliminar imagem',
  confirmDeleteImage: 'Tem certeza que deseja eliminar esta imagem?',
  imageDeleteSuccess: 'Imagem eliminada com sucesso',
  imageDeleteError: 'Erro ao eliminar imagem',
  deleteSelected: 'Eliminar Selecionadas',
  cancelSelection: 'Cancelar',
  noImagesSelected: 'Selecione as imagens para eliminar',
  selectedCount: 'Selecionadas: ',
  // New translations for colaborador reports
  circumstances: 'Circunstâncias',
  contactLocation: 'Local de Contacto',
  rolaAdultaCantando: 'Rola adulta a cantar',
  rolaEmVoo: 'Rola em voo',
  adultoPousado: 'Adulto pousado',
  adultoEmDisplay: 'Adulto em display',
  ninhoVazio: 'Ninho vazio',
  nichoOcupado: 'Nicho ocupado',
  ovos: 'Ovos',
  adultoAIncubar: 'Adulto a incubar',
  crias: 'Crias',
  juvenile: 'Juvenil',
  outraQual: 'Outra',
  arvore: 'Árvore',
  arbusto: 'Arbusto',
  pontoDeAgua: 'Ponto de Água',
  clareira: 'Clareira',
  parcelaAgricola: 'Parcela Agrícola',
  // Weather translations
  weatherConditions: 'Tempo',
  temperature: 'Temperatura',
  humidity: 'Humidade',
  pressure: 'Pressão',
  windSpeed: 'Vento',
  visibility: 'Visibilidade',
  cloudiness: 'Nebulosidade',
  noReportsOffline: 'Não é possível carregar relatórios sem ligação à Internet',
};

export default function ReportsScreen() {
  const { user, userRole } = useAuth();
  const { hasInternetConnection } = useNetwork();
  
  // ALL hooks must be called before any conditional returns
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [isMapModalVisible, setMapModalVisible] = useState(false);
  const [mapReady, setMapReady] = useState(false);
  const mapRef = useRef<MapView>(null);
  const markerRef = useRef<MapMarker>(null);
  const [currentRegion, setCurrentRegion] = useState({
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421
  });
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isImageViewerVisible, setImageViewerVisible] = useState(false);
  const screenWidth = Dimensions.get('window').width;
  const [selectionMode, setSelectionMode] = useState(false);
  const [selectedImageIndexes, setSelectedImageIndexes] = useState<number[]>([]);
  const [activeReport, setActiveReport] = useState<Report | null>(null);
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const [mapType, setMapType] = useState<MapType>('standard');

  useEffect(() => {
    if (!user) return;

    const db = getFirestore(app);
    const reportsRef = collection(db, 'reports');
    const q = query(
      reportsRef,
      where('userId', '==', user.uid),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const reportsList: Report[] = [];
      snapshot.forEach((doc) => {
        reportsList.push({ id: doc.id, ...doc.data() } as Report);
      });
      setReports(reportsList);
      setLoading(false);
      setRefreshing(false);
    }, (error) => {
      // Only log errors that aren't related to being offline
      if (error.code !== 'unavailable' && !error.message.includes('client is offline')) {
        console.error('Error fetching reports:', error);
      }
      setLoading(false);
      setRefreshing(false);
    });

    return () => unsubscribe();
  }, [user]);

  useEffect(() => {
    if (mapReady && markerRef.current && selectedReport) {
      setTimeout(() => {
        markerRef.current?.showCallout();
      }, 100);
    }
  }, [mapReady, selectedReport]);

  useEffect(() => {
    if (mapReady && selectedReport && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: selectedReport.location.latitude,
        longitude: selectedReport.location.longitude,
        latitudeDelta: currentRegion.latitudeDelta,
        longitudeDelta: currentRegion.longitudeDelta,
      }, 1000);
    }
  }, [selectedReport, mapReady, currentRegion]);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Refresh will happen automatically through the snapshot listener
    setRefreshing(false);
  }, []);

  // If user is a technician, show technician reports
  if (userRole === 'tecnico_prorola') {
    return <TechnicianReportsScreen />;
  }

  // Early return for gestor users - show GestorReports component
  if (userRole === 'gestor_caca') {
    return <GestorReportsScreen />;
  }



  const formatDate = (timestamp: Timestamp | null) => {
    if (!timestamp) {
      return 'Data não disponível';
    }
    const date = timestamp.toDate();
    return date.toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Helper function to get selected circumstance
  const getSelectedCircumstance = (circumstances?: ReportCircumstances): string => {
    if (!circumstances) return '';
    
    const selected = Object.entries(circumstances).find(([key, value]) => 
      key !== 'outraQualText' && value === true
    );
    
    if (selected) {
      const [key] = selected;
      if (key === 'outraQual' && circumstances.outraQualText) {
        return `${pt.outraQual}: ${circumstances.outraQualText}`;
      }
      // Type-safe lookup with fallback
      const translation = pt[key as keyof typeof pt];
      return typeof translation === 'string' ? translation : key;
    }
    
    return '';
  };

  // Helper function to get selected contact location
  const getSelectedContactLocation = (contactLocation?: ReportContactLocation): string => {
    if (!contactLocation) return '';
    
    const selected = Object.entries(contactLocation).find(([key, value]) => 
      key !== 'outraQualText' && value === true
    );
    
    if (selected) {
      const [key] = selected;
      if (key === 'outraQual' && contactLocation.outraQualText) {
        return `${pt.outraQual}: ${contactLocation.outraQualText}`;
      }
      // Type-safe lookup with fallback
      const translation = pt[key as keyof typeof pt];
      return typeof translation === 'string' ? translation : key;
    }
    
    return '';
  };

  const handleReportPress = (report: Report) => {
    Alert.alert(
      pt.reportOptions,
      undefined,
      [
        {
          text: pt.view,
          onPress: () => {
            setSelectedReport(report);
            setMapModalVisible(true);
          },
        },
        {
          text: pt.delete,
          style: 'destructive',
          onPress: () => handleDelete(report),
        },
        {
          text: pt.cancel,
          style: 'cancel',
        },
      ]
    );
  };

  const handleDelete = async (report: Report) => {
    showAlert({
      type: 'warning',
      message: pt.confirmDelete,
      onConfirm: async () => {
        try {
          const reportRef = doc(db, 'reports', report.id);
          
          // Delete images from storage if they exist
          if (report.images && report.images.length > 0) {
            for (const imageUrl of report.images) {
              try {
                // Extract the encoded path from Firebase Storage URL
                const matches = imageUrl.match(/o\/(.*?)\?/);
                if (matches && matches[1]) {
                  // Decode the URL-encoded path
                  const decodedPath = decodeURIComponent(matches[1]);
                  const imageRef = storageRef(storage, decodedPath);
                  await deleteObject(imageRef);
                } else {
                  console.error('Could not parse image URL:', imageUrl);
                }
              } catch (deleteError) {
                console.error('Error deleting image:', deleteError, imageUrl);
                // Continue with other images even if one fails
              }
            }
          }

          // Delete the report document
          await deleteDoc(reportRef);
          
          // Immediately update local state to remove the report from the list
          // This provides instant feedback while Firestore listener updates
          setReports(prevReports => prevReports.filter(r => r.id !== report.id));
          
          showAlert({
            type: 'success',
            message: pt.deleteSuccess
          });
        } catch (error) {
          console.error('Error deleting report:', error);
          showAlert({
            type: 'error',
            message: pt.deleteError
          });
        }
      }
    });
  };

  const handleViewImages = (images: string[]) => {
    setSelectedImages(images);
    setImageViewerVisible(true);
  };

  const handleImageLongPress = (report: Report, index: number) => {
    setSelectionMode(true);
    setSelectedImageIndexes([index]);
    setActiveReport(report);
  };

  const handleImageSelect = (index: number) => {
    setSelectedImageIndexes(prev => {
      const isSelected = prev.includes(index);
      if (isSelected) {
        return prev.filter(i => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  const handleDeleteSelectedImages = async () => {
    if (selectedImageIndexes.length === 0) {
      showAlert({
        type: 'error',
        message: t('noImagesSelected')
      });
      return;
    }

    showAlert({
      type: 'warning',
      message: t('confirmDeleteImages'),
      onConfirm: async () => {
        try {
          const sortedIndexes = [...selectedImageIndexes].sort((a, b) => b - a);
          const updatedImages = [...(activeReport?.images || [])];
          
          for (const index of sortedIndexes) {
            const imageUrl = activeReport?.images?.[index];
            if (imageUrl) {
              try {
                // Extract the encoded path from Firebase Storage URL
                const matches = imageUrl.match(/o\/(.*?)\?/);
                if (matches && matches[1]) {
                  // Decode the URL-encoded path
                  const decodedPath = decodeURIComponent(matches[1]);
                  const imageRef = storageRef(storage, decodedPath);
                  await deleteObject(imageRef);
                } else {
                  console.error('Could not parse image URL:', imageUrl);
                }
              } catch (deleteError) {
                console.error('Error deleting image from storage:', deleteError);
              }
              
              updatedImages.splice(index, 1);
            }
          }

          const reportRef = doc(db, 'reports', activeReport?.id || '');
          await updateDoc(reportRef, { images: updatedImages });

          setSelectionMode(false);
          setSelectedImageIndexes([]);
          setActiveReport(null);
          
          showAlert({
            type: 'success',
            message: t('imagesDeleted')
          });
        } catch (error) {
          console.error('Error deleting images:', error);
          showAlert({
            type: 'error',
            message: t('deleteImagesError')
          });
        }
      }
    });
  };

  const renderItem = ({ item }: { item: Report }) => (
    <View style={styles.reportCard}>
      <View style={styles.dateRow}>
        <View style={styles.dateContainer}>
          <Text style={styles.reportDate}>
            {pt.date}: {formatDate(item.createdAt)}
          </Text>
          {/* Display weather info under date */}
          {item.weather && (
            <Text style={styles.weatherInfo}>
              Tempo: {item.weather.temperature}°C, {item.weather.description}
            </Text>
          )}
        </View>
          <TouchableOpacity
            style={styles.deleteReportButton}
            onPress={() => handleDelete(item)}>
            <FontAwesome name="trash" size={20} color="#ff3b30" />
          </TouchableOpacity>
      </View>
      
      <View style={styles.locationContainer}>
        <Text style={styles.locationLabel}>{pt.location}:</Text>
        <TouchableOpacity
          style={styles.mapButton}
          onPress={() => {
            setSelectedReport(item);
            setMapModalVisible(true);
          }}>
          <FontAwesome name="map-marker" size={20} color="#0996a8" />
          <Text style={styles.mapButtonText}>Ver no mapa</Text>
        </TouchableOpacity>
      </View>
      
            {/* Display colaborador report specific fields */}
      {item.circumstances && (
        <View style={styles.reportField}>
          <Text style={styles.fieldLabel}>{pt.circumstances}:</Text>
          <Text style={styles.fieldValue}>{getSelectedCircumstance(item.circumstances)}</Text>
        </View>
      )}
      
      {item.contactLocation && (
        <View style={styles.reportField}>
          <Text style={styles.fieldLabel}>{pt.contactLocation}:</Text>
          <Text style={styles.fieldValue}>{getSelectedContactLocation(item.contactLocation)}</Text>
        </View>
      )}
      
      {item.comment && (
        <Text style={styles.reportComment}>
          {pt.comment}: {item.comment}
        </Text>
      )}

      {item.images && item.images.length > 0 && (
        <>
          <View style={styles.thumbnailContainer}>
            {item.images.map((imageUrl, index) => (
              <Pressable
                key={index}
                style={[
                  styles.thumbnailWrapper,
                  selectionMode && activeReport?.id === item.id && {
                    opacity: selectedImageIndexes.includes(index) ? 0.7 : 1
                  }
                ]}
                onLongPress={() => handleImageLongPress(item, index)}
                onPress={() => {
                  if (selectionMode && activeReport?.id === item.id) {
                    handleImageSelect(index);
                  } else {
                    handleViewImages(item.images || []);
                  }
                }}>
                <LoadingImage
                  source={{ uri: imageUrl }}
                  style={styles.thumbnailImage}
                  placeholderIcon="image"
                  placeholderIconSize={20}
                />
                {selectionMode && 
                 activeReport?.id === item.id && 
                 selectedImageIndexes.includes(index) && (
                  <View style={styles.selectedOverlay}>
                    <View style={styles.checkIcon}>
                      <FontAwesome name="check" size={24} color="#0996a8" />
                    </View>
                  </View>
                )}
              </Pressable>
            ))}
          </View>
          
          {selectionMode && activeReport?.id === item.id && (
            <View style={styles.selectionControls}>
              <Text style={styles.selectedCount}>
                {pt.selectedCount}{selectedImageIndexes.length}
              </Text>
              <View style={styles.selectionButtons}>
                <TouchableOpacity
                  style={[styles.selectionButton, styles.cancelButton]}
                  onPress={() => {
                    setSelectionMode(false);
                    setSelectedImageIndexes([]);
                    setActiveReport(null);
                  }}>
                  <FontAwesome name="times" size={16} color="#fff" style={styles.buttonIcon} />
                  <Text style={styles.selectionButtonText}>{pt.cancel}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.selectionButton, 
                    styles.deleteButton,
                    selectedImageIndexes.length === 0 && styles.disabledButton
                  ]}
                  disabled={selectedImageIndexes.length === 0}
                  onPress={handleDeleteSelectedImages}>
                  <FontAwesome name="trash" size={16} color="#fff" style={styles.buttonIcon} />
                  <Text style={styles.selectionButtonText}>{pt.delete}</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </>
      )}
    </View>
  );

  const renderEmptyComponent = () => {
    if (loading) return null;
    
    if (!hasInternetConnection()) {
      return (
        <View style={styles.emptyContainer}>
          <View style={styles.noInternetCard}>
            <FontAwesome6 
              name="wifi" 
              size={48} 
              color="#ff9500" 
              style={{ opacity: 0.6 }}
            />
            <Text style={styles.noInternetText}>
              Não é possível carregar relatórios sem ligação à Internet
            </Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <View style={styles.modernEmptyCard}>
          <View style={styles.iconContainer}>
            <FontAwesome 
              name="list" 
              size={32} 
              color="#0996a8" 
            />
          </View>
          <View style={styles.titleContainer}>
            <FontAwesome6 
              name="circle-info" 
              size={18} 
              color="#7f8c8d" 
              style={{ marginRight: 8, marginTop: 0 }}
            />
            <Text style={styles.modernTitle}>Sem relatórios</Text>
          </View>
          <Text style={styles.modernSubtitle}>
            Os seus relatórios aparecerão aqui quando forem criados
          </Text>
          
          <View style={styles.infoSection}>
            <Text style={styles.infoTitle}>Como criar um relatório:</Text>
            <View style={styles.infoItem}>
              <FontAwesome6 name="arrow-right" size={14} color="#0996a8" />
              <Text style={styles.infoText}>Vá para a página de Localização</Text>
            </View>
            <View style={styles.infoItem}>
              <FontAwesome6 name="list" size={14} color="#0996a8" />
              <Text style={styles.infoText}>Selecione as circunstâncias</Text>
            </View>
            <View style={styles.infoItem}>
              <FontAwesome6 name="location-dot" size={14} color="#0996a8" />
              <Text style={styles.infoText}>Escolha o local de contacto</Text>
            </View>
            <View style={styles.infoItem}>
              <FontAwesome6 name="camera" size={14} color="#0996a8" />
              <Text style={styles.infoText}>Adicione fotos (se possível)</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  if (Platform.OS === 'web') {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>
          O mapa não está disponível na versão web.
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#0996a8" />
        <Text style={styles.loadingText}>
          {pt.loading}
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>
          {error}
        </Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setLoading(true);
            setError(null);
          }}>
          <Text style={styles.retryButtonText}>{pt.retry}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <FlatList
        data={reports || []}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={(!reports || reports.length === 0) ? styles.emptyContainer : styles.listContainer}
        style={styles.list}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#0996a8']}
            tintColor="#0996a8"
          />
        }
        ListEmptyComponent={renderEmptyComponent}
      />

      <Modal
        animationType="slide"
        transparent={false}
        visible={isMapModalVisible}
        onRequestClose={() => setMapModalVisible(false)}>
        <View style={styles.container}>
          {selectedReport && MapView && (
            <>
              <MapView
                ref={mapRef}
                style={styles.map}
                initialRegion={{
                  latitude: selectedReport.location.latitude,
                  longitude: selectedReport.location.longitude,
                  latitudeDelta: 0.5,
                  longitudeDelta: 0.5,
                }}
                onMapReady={() => {
                  setMapReady(true);
                }}
                onRegionChangeComplete={(region) => {
                  setCurrentRegion({
                    latitudeDelta: region.latitudeDelta,
                    longitudeDelta: region.longitudeDelta
                  });
                }}
                mapType={mapType}
                provider={PROVIDER_GOOGLE}>
                {mapReady && (
                  <Marker
                    ref={markerRef}
                    coordinate={{
                      latitude: selectedReport.location.latitude,
                      longitude: selectedReport.location.longitude,
                    }}
                    title="Relatório"
                    description={selectedReport.comment}
                    tracksViewChanges={false}
                  />
                )}
              </MapView>
              <View style={styles.mapControls}>
                <TouchableOpacity
                  style={styles.mapTypeButton}
                  onPress={() => setMapType(mapType === 'standard' ? 'satellite' : 'standard')}>
                  <FontAwesome 
                    name={mapType === 'standard' ? 'map' : 'globe'} 
                    size={16} 
                    color="#FFFFFF" 
                    style={styles.buttonIcon} 
                  />
                  <Text style={styles.mapTypeButtonText}>
                    {mapType === 'standard' ? 'Satélite' : 'Mapa'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    setMapModalVisible(false);
                    setMapReady(false);
                  }}>
                  <FontAwesome name="times" size={16} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.closeButtonText}>Fechar</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </Modal>

      <Modal
        visible={isImageViewerVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setImageViewerVisible(false)}
      >
        <View style={styles.imageViewerContainer}>
          <View style={styles.imageViewerHeader}>
            <TouchableOpacity
              style={styles.imageViewerCloseButton}
              onPress={() => setImageViewerVisible(false)}
            >
              <Text style={styles.closeButtonText}>{pt.closeImages}</Text>
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.imageViewerScrollView}>
            {selectedImages.map((imageUrl, index) => (
              <LoadingImage
                key={index}
                source={{ uri: imageUrl }}
                style={[styles.fullImage, { width: screenWidth, height: screenWidth }]}
                placeholderIcon="image"
                placeholderIconSize={32}
                containerStyle={{ backgroundColor: '#000' }}
              />
            ))}
          </ScrollView>
        </View>
      </Modal>

      <CustomAlert
        visible={isVisible}
        type={config.type}
        message={config.message}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    marginTop: -25,
  },
  list: {
    backgroundColor: '#ffffff',
  },
  listContainer: {
    padding: 15,
    marginTop: 15,
    paddingBottom: 80, // Add extra padding for tab bar
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  noReportsText: {
    fontSize: 16,
    color: '#666',
  },
  reportCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },

  deleteReportButton: {
    backgroundColor: '#fff',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  dateRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dateContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  reportDate: {
    fontSize: 14,
    color: '#666',
  },
  weatherInfo: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  reportComment: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#666',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
    color: '#666',
  },
  retryButton: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: 'absolute',
    top: 40,
    right: 20,
    gap: 10,
  },
  mapTypeButton: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  mapTypeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  closeButton: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonIcon: {
    marginRight: 8,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingTop: 20,
    paddingBottom: 100,
    paddingHorizontal: 16,
    minHeight: 300,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationLabel: {
    fontSize: 14,
    color: '#666',
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8f5f3',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginLeft: 8,
  },
  mapButtonText: {
    color: '#0996a8',
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
  thumbnailContainer: {
    marginTop: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  thumbnailWrapper: {
    width: '31%',
    aspectRatio: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  selectedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 150, 136, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkIcon: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectionControls: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  selectedCount: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  selectionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  selectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    minWidth: 100,
  },
  selectionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  cancelButton: {
    backgroundColor: '#757575',
  },
  deleteButton: {
    backgroundColor: '#ff3b30',
  },
  disabledButton: {
    opacity: 0.5,
  },
  imageViewerContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  imageViewerHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
    padding: 20,
    paddingTop: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  imageViewerCloseButton: {
    alignSelf: 'flex-end',
    backgroundColor: '#757575',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  imageViewerScrollView: {
    flex: 1,
    marginTop: 80,
  },
  fullImage: {
    marginVertical: 10,
  },
  reportField: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    marginRight: 8,
    flexShrink: 0,
    minWidth: 80,
  },
  fieldValue: {
    fontSize: 14,
    color: '#666',
    flex: 1,
    flexWrap: 'wrap',
  },
  modernNoInternetCard: {
    backgroundColor: '#fef7f0',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    marginHorizontal: 24,
    borderWidth: 1,
    borderColor: '#f4e4d6',
    maxWidth: 320,
  },
  modernTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#5a6c7d',
    marginTop: 16,
    marginBottom: 6,
    textAlign: 'center',
  },
  modernSubtitle: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'left',
    lineHeight: 15,
    marginTop: 2,
  },
  modernEmptyCard: {
    backgroundColor: '#f8fffe',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    marginHorizontal: 16,
    borderWidth: 1,
    borderColor: '#e8f4f3',
    width: '100%',
    minHeight: 240,
  },
  iconContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 14,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 2,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'center',
  },
  infoSection: {
    marginTop: 20,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#7f8c8d',
    marginBottom: 12,
    textAlign: 'center',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 13,
    color: '#7f8c8d',
    marginLeft: 10,
  },
  noInternetCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#ff9500',
    marginHorizontal: 20,
  },
  noInternetText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginVertical: 16,
  },
  restrictedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#f8fafc',
  },
  restrictedTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  restrictedText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 8,
  },
  comingSoonBadge: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 16,
  },
  comingSoonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
});