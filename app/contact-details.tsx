import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput, ScrollView, StyleSheet, Platform, Image, ActivityIndicator, Alert } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import LoadingImage from '@/components/LoadingImage';
import PhotoSelectionAlert from '@/components/PhotoSelectionAlert';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import { ReportCircumstances, ReportContactLocation } from '@/types/reports';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { storage } from '@/config/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import NetInfo from '@react-native-community/netinfo';
import { useAuth } from '@/contexts/AuthContext';

interface ContactDetailsData {
  circumstances: ReportCircumstances;
  contactLocation: ReportContactLocation;
  images: string[];
}

// Portuguese translations
const pt = {
  contactDetails: 'Detalhes do Contacto',
  reportContactCircumstances: 'Indique em que circunstância se deu o contacto',
  reportContactLocation: 'Indique onde se deu contacto',
  rolaAdultaCantando: 'Rola adulta a cantar',
  rolaEmVoo: 'Rola em voo',
  adultoPousado: 'Adulto pousado',
  adultoEmDisplay: 'Adulto em display',
  ninhoVazio: 'Ninho vazio',
  nichoOcupado: 'Nicho ocupado',
  ovos: 'Ovos',
  adultoAIncubar: 'Adulto a incubar',
  crias: 'Crias',
  juvenile: 'Juvenil',
  outraQual: 'Outra. Qual?',
  arvore: 'Árvore',
  arbusto: 'Arbusto',
  pontoDeAgua: 'Ponto de Água',
  clareira: 'Clareira',
  parcelaAgricola: 'Parcela Agrícola',
  addImages: 'Anexar fotos',
  selectAtLeastOne: 'Por favor, selecione pelo menos uma opção de cada secção',
  maxImagesReached: 'Máximo de 6 fotos atingido',
  confirmDeletePhoto: 'Tem certeza que deseja eliminar esta foto?',
  cancel: 'Cancelar',
  save: 'Adicionar',
  distance: 'Distância',
  bearing: 'Direção',
  uploadingImages: 'A carregar fotos...',
};

export default function ContactDetailsScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { user, userRole } = useAuth();
  const { isVisible, config, showAlert, hideAlert } = useCustomAlert();
  const scrollViewRef = useRef<ScrollView>(null);
  const [showPhotoAlert, setShowPhotoAlert] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get params
  const distance = parseFloat(params.distance as string) || 0;
  const bearing = params.bearing ? parseFloat(params.bearing as string) : undefined;
  const source = params.source as string;
  
  console.log('🔄 CONTACT_DETAILS: Screen loaded with params:', params);
  console.log('🔄 CONTACT_DETAILS: Source parameter:', source);

  // State for circumstances
  const [circumstances, setCircumstances] = useState<ReportCircumstances>({
    rolaAdultaCantando: false,
    rolaEmVoo: false,
    adultoPousado: false,
    adultoEmDisplay: false,
    ninhoVazio: false,
    nichoOcupado: false,
    ovos: false,
    adultoAIncubar: false,
    crias: false,
    juvenile: false,
    outraQual: false,
    outraQualText: '',
  });

  // State for contact location
  const [contactLocation, setContactLocation] = useState<ReportContactLocation>({
    arvore: false,
    arbusto: false,
    pontoDeAgua: false,
    clareira: false,
    parcelaAgricola: false,
    outraQual: false,
    outraQualText: '',
  });

  // State for images
  const [images, setImages] = useState<string[]>([]);

  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      return `${(meters / 1000).toFixed(2)}km`;
    }
  };

  const getBearingText = (degrees: number): string => {
    const directions = [
      'N', 'NNE', 'NE', 'ENE',
      'E', 'ESE', 'SE', 'SSE',
      'S', 'SSO', 'SO', 'OSO',
      'O', 'ONO', 'NO', 'NNO'
    ];
    
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  };

  const handleCircumstanceChange = (key: keyof ReportCircumstances, value: boolean | string) => {
    if (key === 'outraQualText') {
      setCircumstances(prev => ({
        ...prev,
        [key]: value as string
      }));
    } else {
      if (value === true) {
        const resetCircumstances: ReportCircumstances = {
          rolaAdultaCantando: false,
          rolaEmVoo: false,
          adultoPousado: false,
          adultoEmDisplay: false,
          ninhoVazio: false,
          nichoOcupado: false,
          ovos: false,
          adultoAIncubar: false,
          crias: false,
          juvenile: false,
          outraQual: false,
          outraQualText: '',
        };
        
        setCircumstances({
          ...resetCircumstances,
          [key]: true,
          outraQualText: key === 'outraQual' ? circumstances.outraQualText : '',
        });
      } else {
        const resetCircumstances: ReportCircumstances = {
          rolaAdultaCantando: false,
          rolaEmVoo: false,
          adultoPousado: false,
          adultoEmDisplay: false,
          ninhoVazio: false,
          nichoOcupado: false,
          ovos: false,
          adultoAIncubar: false,
          crias: false,
          juvenile: false,
          outraQual: false,
          outraQualText: '',
        };
        
        setCircumstances(resetCircumstances);
      }
    }
  };

  const handleContactLocationChange = (key: keyof ReportContactLocation, value: boolean | string) => {
    if (key === 'outraQualText') {
      setContactLocation(prev => ({
        ...prev,
        [key]: value as string
      }));
    } else {
      if (value === true) {
        const resetContactLocation: ReportContactLocation = {
          arvore: false,
          arbusto: false,
          pontoDeAgua: false,
          clareira: false,
          parcelaAgricola: false,
          outraQual: false,
          outraQualText: '',
        };
        
        setContactLocation({
          ...resetContactLocation,
          [key]: true,
          outraQualText: key === 'outraQual' ? contactLocation.outraQualText : '',
        });
      } else {
        const resetContactLocation: ReportContactLocation = {
          arvore: false,
          arbusto: false,
          pontoDeAgua: false,
          clareira: false,
          parcelaAgricola: false,
          outraQual: false,
          outraQualText: '',
        };
        
        setContactLocation(resetContactLocation);
      }
    }
  };

  const validateForm = (): boolean => {
    const hasCircumstance = Object.entries(circumstances).some(([key, value]) => 
      key !== 'outraQualText' && value === true
    );

    const hasContactLocation = Object.entries(contactLocation).some(([key, value]) => 
      key !== 'outraQualText' && value === true
    );

    return hasCircumstance && hasContactLocation;
  };

  const handleSubmit = async () => {
    console.log('🔄 CONTACT_DETAILS: handleSubmit called');
    if (!validateForm()) {
      showAlert({
        type: 'error',
        message: pt.selectAtLeastOne,
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Get observer and contact locations from params
      const observerLocation = {
        latitude: parseFloat(params.observerLat as string),
        longitude: parseFloat(params.observerLng as string),
      };
      const contactLocationCoords = {
        latitude: parseFloat(params.contactLat as string),
        longitude: parseFloat(params.contactLng as string),
      };
      
      // Get current monitoring session data
      const sessionData = await AsyncStorage.getItem('currentMonitoringSession');
      const session = sessionData ? JSON.parse(sessionData) : null;
      
      if (!session) {
        showAlert({
          type: 'error',
          message: 'Sessão de monitorização não encontrada',
        });
        return;
      }
      
      // Check network connectivity first
      const netInfo = await NetInfo.fetch();
      let imagesToSave: string[] = [];
      
      // Always save local image paths for deferred upload - no immediate processing
        if (images && images.length > 0) {
        console.log(`📸 Fast save: Storing ${images.length} local image paths for background sync...`);
        imagesToSave = [...images]; // Save local paths for later processing
      }
      
      // Get current contact count - use appropriate storage key based on user role
      const isGestorUser = userRole === 'gestor_caca';
      const storageKey = isGestorUser ? 'gestorOfflineContactEvents' : 'offlineContactEvents';
      const contactEvents = await AsyncStorage.getItem(storageKey);
      const events = contactEvents ? JSON.parse(contactEvents) : [];
      const contactNumber = events.filter((e: any) => e.sessionId === session.sessionId).length + 1;
      
      // Create complete contact event with location and details
      // Add a small delay to ensure unique timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
      const contactTimestamp = new Date();
      console.log('📅 CONTACT_DETAILS: Creating contact with timestamp:', contactTimestamp.toISOString());
      console.log('📅 CONTACT_DETAILS: Timestamp milliseconds:', contactTimestamp.getTime());
      
      const completeContactEvent = {
        sessionId: session.sessionId,
        timestamp: contactTimestamp.toISOString(),
        observerLocation,
        contactLocation: contactLocationCoords,
        distance,
        bearing,
        contactNumber,
        circumstances,
        contactLocationDetails: {
          ...contactLocation, // This includes the arvore/arbusto selections
          latitude: contactLocationCoords.latitude,
          longitude: contactLocationCoords.longitude,
        },
        images: imagesToSave, // Use either uploaded URLs or local paths
        userId: user?.uid || 'unknown_user',
        userName: user?.displayName || user?.email || 'Unknown User',
        userRole: userRole || 'colaborador',
        protocol: session.protocol,
        source: 'mobile', // Mark as GPS/mobile created
        // Add sync status to track if images need uploading
        needsImageSync: !netInfo.isConnected && imagesToSave.length > 0,
      };

      // Validate that we have essential data before saving
      if (!completeContactEvent.userId || completeContactEvent.userId === 'unknown_user') {
        console.error('❌ Cannot save contact event: Missing valid userId');
        throw new Error('Invalid user data - cannot save contact event');
      }

      // Save to contact events - use appropriate storage key
      events.push(completeContactEvent);
      await AsyncStorage.setItem(storageKey, JSON.stringify(events));
      
      console.log('🔄 CONTACT_DETAILS: Contact saved successfully');
      console.log('🔄 CONTACT_DETAILS: Storage key used:', storageKey);
      console.log('🔄 CONTACT_DETAILS: Total events now:', events.length);
      console.log('🔄 CONTACT_DETAILS: Last 3 contact timestamps:', 
        events.slice(-3).map((e: any) => ({ contactNumber: e.contactNumber, timestamp: e.timestamp }))
      );
      
      // Show appropriate feedback based on connectivity
      if (!netInfo.isConnected && imagesToSave.length > 0) {
        console.log('📱 Contact saved offline - images will be uploaded when internet is available');
      } else {
        console.log('✅ Contact saved successfully');
      }
      
      // Navigate appropriately based on source
      console.log('🔄 CONTACT_DETAILS: Navigation source:', source);
      console.log('🔄 CONTACT_DETAILS: About to navigate back');
      
      if (source === 'gestor_monitoring') {
        console.log('🔄 CONTACT_DETAILS: Gestor monitoring - using router.back()');
        // For gestor monitoring, we need to dismiss the contact screen and return to the modal
        // Since the monitoring is happening in a modal, we just go back
        router.back();
      } else {
        console.log('🔄 CONTACT_DETAILS: Regular monitoring - using router.back()');
        // For regular technician monitoring, go back normally
        router.back();
      }
    } catch (error) {
      console.error('Error saving contact:', error);
      showAlert({
        type: 'error',
        message: 'Erro ao guardar contacto. Tente novamente.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    router.back();
  };

  const handleAddImages = () => {
    if (images.length >= 6) {
      showAlert({
        type: 'warning',
        message: pt.maxImagesReached,
      });
      return;
    }
    setShowPhotoAlert(true);
  };

  const pickImage = async (source: 'camera' | 'gallery') => {
    try {
      let result;
      
      if (source === 'camera') {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          showAlert({
            type: 'error',
            message: 'Desculpe, precisamos de permissões da câmara para isto funcionar!',
          });
          return;
        }
        
        result = await ImagePicker.launchCameraAsync({
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.7,
        });
      } else {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          showAlert({
            type: 'error',
            message: 'Desculpe, precisamos de permissões da galeria para isto funcionar!',
          });
          return;
        }
        
        result = await ImagePicker.launchImageLibraryAsync({
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.7,
        });
      }

      if (!result.canceled && result.assets[0]) {
        setImages(prev => [...prev, result.assets[0].uri]);
        
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    } catch (error) {
      console.error('Erro ao selecionar imagem:', error);
    }
  };

  const handleDeletePhoto = (index: number) => {
    showAlert({
      type: 'warning',
      message: pt.confirmDeletePhoto,
      onConfirm: () => {
        setImages(prev => prev.filter((_, i) => i !== index));
      }
    });
  };

  const renderCheckboxItem = (
    key: string,
    label: string,
    checked: boolean,
    onToggle: () => void,
    showTextInput: boolean = false,
    textValue: string = '',
    onTextChange?: (text: string) => void
  ) => (
    <View key={key} style={styles.checkboxContainer}>
      <TouchableOpacity
        style={[styles.checkboxRow, checked && styles.checkboxRowSelected]}
        onPress={onToggle}
        activeOpacity={0.7}
      >
        <View style={[styles.checkbox, checked && styles.checkboxChecked]}>
          {checked && <FontAwesome name="check" size={14} color="#fff" />}
        </View>
        <Text style={[styles.checkboxLabel, checked && styles.checkboxLabelSelected]}>
          {label}
        </Text>
      </TouchableOpacity>
      
      {showTextInput && checked && (
        <View style={styles.textInputContainer}>
          <TextInput
            style={styles.checkboxTextInput}
            placeholder="Especifique..."
            placeholderTextColor="#999"
            value={textValue}
            onChangeText={onTextChange}
            multiline
            numberOfLines={3}
          />
        </View>
      )}
    </View>
  );

  const getSelectedCircumstance = () => {
    const selected = Object.entries(circumstances).find(([key, value]) => 
      key !== 'outraQualText' && value === true
    )?.[0] as keyof ReportCircumstances | undefined;
    
    return selected;
  };

  const getSelectedContactLocation = () => {
    const selected = Object.entries(contactLocation).find(([key, value]) => 
      key !== 'outraQualText' && value === true
    )?.[0] as keyof ReportContactLocation | undefined;
    
    return selected;
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen 
        options={{
          title: pt.contactDetails,
          headerStyle: { backgroundColor: '#0996a8' },
          headerTintColor: '#fff',
          headerTitleStyle: { fontWeight: 'bold' },
          headerBackVisible: true,
        }}
      />

      {/* Distance and bearing info */}
      <View style={styles.distanceContainer}>
        <View style={styles.distanceRow}>
          <FontAwesome name="arrows-h" size={16} color="#666" />
          <Text style={styles.distanceText}>
            {pt.distance}: {formatDistance(distance)}
          </Text>
          {bearing !== undefined && (
            <>
              <FontAwesome name="compass" size={16} color="#666" style={styles.compassIcon} />
              <Text style={styles.distanceText}>
                {pt.bearing}: {Math.round(bearing)}° {getBearingText(bearing)}
              </Text>
            </>
          )}
        </View>
      </View>

      {/* Content wrapper */}
      <View style={styles.contentWrapper}>
        <ScrollView 
          ref={scrollViewRef}
          style={styles.scrollContent}
          contentContainerStyle={styles.scrollContentContainer}
          showsVerticalScrollIndicator={false}
          bounces={true}
          keyboardShouldPersistTaps="handled"
        >
          {/* Circumstances Section */}
          <View style={styles.checkboxSection}>
            <Text style={styles.sectionTitle}>{pt.reportContactCircumstances}</Text>
            
            <View style={styles.checkboxSection}>
              {(() => {
                const selectedCircumstance = getSelectedCircumstance();
                
                if (selectedCircumstance) {
                  return renderCheckboxItem(
                    selectedCircumstance,
                    pt[selectedCircumstance as keyof typeof pt],
                    true,
                    () => handleCircumstanceChange(selectedCircumstance, false),
                    selectedCircumstance === 'outraQual',
                    circumstances.outraQualText,
                    (text) => handleCircumstanceChange('outraQualText', text)
                  );
                }
                
                return (
                  <>
                    {renderCheckboxItem(
                      'rolaAdultaCantando',
                      pt.rolaAdultaCantando,
                      circumstances.rolaAdultaCantando,
                      () => handleCircumstanceChange('rolaAdultaCantando', !circumstances.rolaAdultaCantando)
                    )}
                    
                    {renderCheckboxItem(
                      'rolaEmVoo',
                      pt.rolaEmVoo,
                      circumstances.rolaEmVoo,
                      () => handleCircumstanceChange('rolaEmVoo', !circumstances.rolaEmVoo)
                    )}
                    
                    {renderCheckboxItem(
                      'adultoPousado',
                      pt.adultoPousado,
                      circumstances.adultoPousado,
                      () => handleCircumstanceChange('adultoPousado', !circumstances.adultoPousado)
                    )}
                    
                    {renderCheckboxItem(
                      'adultoEmDisplay',
                      pt.adultoEmDisplay,
                      circumstances.adultoEmDisplay,
                      () => handleCircumstanceChange('adultoEmDisplay', !circumstances.adultoEmDisplay)
                    )}
                    
                    {renderCheckboxItem(
                      'ninhoVazio',
                      pt.ninhoVazio,
                      circumstances.ninhoVazio,
                      () => handleCircumstanceChange('ninhoVazio', !circumstances.ninhoVazio)
                    )}
                    
                    {renderCheckboxItem(
                      'nichoOcupado',
                      pt.nichoOcupado,
                      circumstances.nichoOcupado,
                      () => handleCircumstanceChange('nichoOcupado', !circumstances.nichoOcupado)
                    )}
                    
                    {renderCheckboxItem(
                      'ovos',
                      pt.ovos,
                      circumstances.ovos,
                      () => handleCircumstanceChange('ovos', !circumstances.ovos)
                    )}
                    
                    {renderCheckboxItem(
                      'adultoAIncubar',
                      pt.adultoAIncubar,
                      circumstances.adultoAIncubar,
                      () => handleCircumstanceChange('adultoAIncubar', !circumstances.adultoAIncubar)
                    )}
                    
                    {renderCheckboxItem(
                      'crias',
                      pt.crias,
                      circumstances.crias,
                      () => handleCircumstanceChange('crias', !circumstances.crias)
                    )}
                    
                    {renderCheckboxItem(
                      'juvenile',
                      pt.juvenile,
                      circumstances.juvenile,
                      () => handleCircumstanceChange('juvenile', !circumstances.juvenile)
                    )}
                    
                    {renderCheckboxItem(
                      'outraQual',
                      pt.outraQual,
                      circumstances.outraQual,
                      () => handleCircumstanceChange('outraQual', !circumstances.outraQual),
                      true,
                      circumstances.outraQualText,
                      (text) => handleCircumstanceChange('outraQualText', text)
                    )}
                  </>
                );
              })()}
            </View>
          </View>

          {/* Contact Location Section */}
          <View style={styles.checkboxSection}>
            <Text style={styles.sectionTitle}>{pt.reportContactLocation}</Text>
            
            <View style={styles.checkboxSection}>
              {(() => {
                const selectedContactLocation = getSelectedContactLocation();
                
                if (selectedContactLocation) {
                  return renderCheckboxItem(
                    selectedContactLocation,
                    pt[selectedContactLocation as keyof typeof pt],
                    true,
                    () => handleContactLocationChange(selectedContactLocation, false),
                    selectedContactLocation === 'outraQual',
                    contactLocation.outraQualText,
                    (text) => handleContactLocationChange('outraQualText', text)
                  );
                }
                
                return (
                  <>
                    {renderCheckboxItem(
                      'arvore',
                      pt.arvore,
                      contactLocation.arvore,
                      () => handleContactLocationChange('arvore', !contactLocation.arvore)
                    )}
                    
                    {renderCheckboxItem(
                      'arbusto',
                      pt.arbusto,
                      contactLocation.arbusto,
                      () => handleContactLocationChange('arbusto', !contactLocation.arbusto)
                    )}
                    
                    {renderCheckboxItem(
                      'pontoDeAgua',
                      pt.pontoDeAgua,
                      contactLocation.pontoDeAgua,
                      () => handleContactLocationChange('pontoDeAgua', !contactLocation.pontoDeAgua)
                    )}
                    
                    {renderCheckboxItem(
                      'clareira',
                      pt.clareira,
                      contactLocation.clareira,
                      () => handleContactLocationChange('clareira', !contactLocation.clareira)
                    )}
                    
                    {renderCheckboxItem(
                      'parcelaAgricola',
                      pt.parcelaAgricola,
                      contactLocation.parcelaAgricola,
                      () => handleContactLocationChange('parcelaAgricola', !contactLocation.parcelaAgricola)
                    )}
                    
                    {renderCheckboxItem(
                      'outraQual',
                      pt.outraQual,
                      contactLocation.outraQual,
                      () => handleContactLocationChange('outraQual', !contactLocation.outraQual),
                      true,
                      contactLocation.outraQualText,
                      (text) => handleContactLocationChange('outraQualText', text)
                    )}
                  </>
                );
              })()}
            </View>
          </View>

          {/* Images Section */}
          <View style={styles.imagesSectionContainer}>
            <Text style={styles.sectionTitle}>{pt.addImages}</Text>
            
            <View style={styles.imagesContainer}>
              {images.map((imageUri, index) => (
                <View key={index} style={styles.imagePreviewContainer}>
                  <LoadingImage
                    source={{ uri: imageUri }}
                    style={styles.imagePreview}
                  />
                  <View style={styles.imageDarkOverlay} />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => handleDeletePhoto(index)}
                  >
                    <FontAwesome name="times" size={12} color="white" />
                  </TouchableOpacity>
                </View>
              ))}
              
              {images.length < 6 && (
                <TouchableOpacity
                  style={styles.addImageButton}
                  onPress={handleAddImages}
                >
                  <FontAwesome name="camera" size={24} color="#0996a8" />
                  <Text style={styles.addImageText}>Adicionar</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </ScrollView>
      </View>

      {/* Fixed buttons at bottom */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.cancelButton} onPress={handleClose}>
          <FontAwesome name="times" size={16} color="#fff" style={styles.buttonIcon} />
          <Text style={styles.cancelButtonText}>{pt.cancel}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.saveButton, isSubmitting && styles.saveButtonDisabled]} 
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator size="small" color="#fff" style={styles.buttonIcon} />
          ) : (
            <FontAwesome name="check" size={16} color="#fff" style={styles.buttonIcon} />
          )}
          <Text style={styles.saveButtonText}>
            {isSubmitting ? 'A guardar...' : pt.save}
          </Text>
        </TouchableOpacity>
      </View>

      <PhotoSelectionAlert
        visible={showPhotoAlert}
        onClose={() => setShowPhotoAlert(false)}
        onTakePhoto={() => {
          setShowPhotoAlert(false);
          pickImage('camera');
        }}
        onChoosePhoto={() => {
          setShowPhotoAlert(false);
          pickImage('gallery');
        }}
      />

      <CustomAlert
        visible={isVisible}
        type={config.type}
        message={config.message}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e8f4f8',
  },
  distanceContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    backgroundColor: '#e8f4f8',
    borderBottomWidth: 1,
    borderBottomColor: '#d0e6ed',
    marginTop: 0,
  },
  distanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 2,
  },
  distanceText: {
    fontSize: 14,
    color: '#0996a8',
    fontWeight: '500',
    marginLeft: 8,
  },
  compassIcon: {
    marginLeft: 8,
  },
  contentWrapper: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingTop: 15,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 15,
    marginTop: 5,
  },
  checkboxSection: {
    marginBottom: 5,
  },
  checkboxContainer: {
    marginBottom: 12,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 10,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  checkboxRowSelected: {
    backgroundColor: '#f0f8ff',
    borderColor: '#0996a8',
    shadowOpacity: 0.1,
    elevation: 2,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#fff',
  },
  checkboxChecked: {
    backgroundColor: '#0996a8',
    borderColor: '#0996a8',
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 16,
    color: '#666',
    lineHeight: 20,
  },
  checkboxLabelSelected: {
    fontWeight: '600',
    color: '#0996a8',
  },
  textInputContainer: {
    marginTop: 0,
  },
  checkboxTextInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 10,
    padding: 12,
    minHeight: 80,
    textAlignVertical: 'top',
    backgroundColor: '#fff',
    color: '#666',
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  imagesSectionContainer: {
    marginTop: 5,
    marginBottom: 0,
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginTop: 10,
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
  imagePreviewContainer: {
    position: 'relative',
    width: 100,
    height: 100,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
  },
  imageDarkOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '30%',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 5,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 59, 48, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  addImageButton: {
    width: 100,
    height: 100,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#0996a8',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5fcfd',
    gap: 4,
  },
  addImageText: {
    fontSize: 12,
    color: '#0996a8',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 12,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 15,
    flexShrink: 0,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#6B7280',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#0996a8',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#9CA3AF',
    opacity: 0.7,
  },
  buttonIcon: {
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 