// Weather service for fetching weather data from OpenWeatherMap API

import { WEATHER_CONFIG, isWeatherConfigured } from '@/config/weather';
import { WeatherConditions } from '@/types/reports';

export interface WeatherData {
  temperature: number;
  feelsLike: number;
  description: string;
  humidity: number;
  pressure: number;
  pressureTrend?: 'rising' | 'falling' | 'steady';
  windSpeed: number;
  windDirection: number;
  windGust?: number;
  visibility: number;
  cloudiness: number;
  uvIndex?: number;
  dewPoint?: number;
  icon: string;
  timestamp: number;
  // Bird watching specific
  sunrise: number;
  sunset: number;
  moonPhase?: number; // 0-1, where 0 and 1 are new moon, 0.5 is full moon
  barometricTrend?: string;
}

export interface ForecastDay {
  date: number; // timestamp
  tempMax: number;
  tempMin: number;
  description: string;
  icon: string;
  humidity: number;
  windSpeed: number;
  windDirection: number;
  uvIndex?: number;
  cloudiness: number;
  sunrise: number;
  sunset: number;
  pressure?: number;
  visibility?: number;
  birdWatchingConditions: string;
  windConditionForBirds: string;
}

export interface WeatherForecast {
  current: WeatherData;
  forecast: ForecastDay[];
}

export interface WeatherError {
  message: string;
  code?: string;
}

class WeatherService {
  async getCurrentWeather(latitude: number, longitude: number): Promise<WeatherData | null> {
    if (!isWeatherConfigured()) {
      console.log('Weather API not configured, skipping weather data');
      return null;
    }

    try {
      // Use One Call API for more comprehensive data
      const url = `https://api.openweathermap.org/data/3.0/onecall?lat=${latitude}&lon=${longitude}&appid=${WEATHER_CONFIG.API_KEY}&units=${WEATHER_CONFIG.UNITS}&lang=${WEATHER_CONFIG.LANGUAGE}&exclude=minutely,hourly,daily,alerts`;
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), WEATHER_CONFIG.TIMEOUT);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        // Fallback to basic current weather API if One Call fails
        return this.getCurrentWeatherBasic(latitude, longitude);
      }

      const data = await response.json();
      const current = data.current;

      return this.sanitizeWeatherData({
        temperature: Math.round(current.temp || 0),
        feelsLike: Math.round(current.feels_like || current.temp || 0),
        description: current.weather?.[0]?.description || 'N/A',
        humidity: current.humidity || 0,
        pressure: current.pressure || 1013,
        pressureTrend: this.calculatePressureTrend(current.pressure || 1013),
        windSpeed: current.wind_speed || 0,
        windDirection: current.wind_deg || 0,
        windGust: current.wind_gust || null,
        visibility: current.visibility ? current.visibility / 1000 : 10, // Convert to km, default 10km
        cloudiness: current.clouds || 0,
        uvIndex: current.uvi || 0,
        dewPoint: current.dew_point ? Math.round(current.dew_point) : null,
        icon: current.weather?.[0]?.icon || '01d',
        timestamp: Date.now(),
        sunrise: current.sunrise ? current.sunrise * 1000 : Date.now(), // Convert to milliseconds
        sunset: current.sunset ? current.sunset * 1000 : Date.now() + (12 * 60 * 60 * 1000),
        moonPhase: this.calculateMoonPhase(),
        barometricTrend: this.getBarometricTrend(current.pressure || 1013),
      });
    } catch (error) {
      // Silent handling of all weather errors - never throw exceptions
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('Weather API request timed out (normal when offline)');
        } else if (error.message.includes('Network request failed') || error.message.includes('TypeError')) {
          console.log('No internet connection for weather data (normal when offline)');
        } else {
          console.log('Weather API unavailable:', error.message);
        }
      } else {
        console.log('Unknown weather API error occurred');
      }
      // Try fallback API, but if that fails too, just return null
      try {
        return this.getCurrentWeatherBasic(latitude, longitude);
      } catch (fallbackError) {
        console.log('Fallback weather API also failed, returning null');
        return null;
      }
    }
  }

  async getWeatherForecast(latitude: number, longitude: number): Promise<WeatherForecast | null> {
    if (!isWeatherConfigured()) {
      console.warn('OpenWeatherMap API key not configured. Please check config/weather.ts');
      return null;
    }

    try {
      // Use One Call API with daily forecast included
      const url = `https://api.openweathermap.org/data/3.0/onecall?lat=${latitude}&lon=${longitude}&appid=${WEATHER_CONFIG.API_KEY}&units=${WEATHER_CONFIG.UNITS}&lang=${WEATHER_CONFIG.LANGUAGE}&exclude=minutely,hourly,alerts`;
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), WEATHER_CONFIG.TIMEOUT);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const current = data.current;

      // Process current weather with safe fallbacks
      const currentWeather: WeatherData = this.sanitizeWeatherData({
        temperature: Math.round(current.temp || 0),
        feelsLike: Math.round(current.feels_like || current.temp || 0),
        description: current.weather?.[0]?.description || 'N/A',
        humidity: current.humidity || 0,
        pressure: current.pressure || 1013,
        pressureTrend: this.calculatePressureTrend(current.pressure || 1013),
        windSpeed: current.wind_speed || 0,
        windDirection: current.wind_deg || 0,
        windGust: current.wind_gust || null,
        visibility: current.visibility ? current.visibility / 1000 : 10,
        cloudiness: current.clouds || 0,
        uvIndex: current.uvi || 0,
        dewPoint: current.dew_point ? Math.round(current.dew_point) : null,
        icon: current.weather?.[0]?.icon || '01d',
        timestamp: Date.now(),
        sunrise: current.sunrise ? current.sunrise * 1000 : Date.now(),
        sunset: current.sunset ? current.sunset * 1000 : Date.now() + (12 * 60 * 60 * 1000),
        moonPhase: this.calculateMoonPhase(),
        barometricTrend: this.getBarometricTrend(current.pressure || 1013),
      });

      // Process 5-day forecast (skip today, get next 5 days) with safe fallbacks
      const forecast: ForecastDay[] = (data.daily || []).slice(1, 6).map((day: any) => ({
        date: day.dt ? day.dt * 1000 : Date.now(),
        tempMax: Math.round(day.temp?.max || 20),
        tempMin: Math.round(day.temp?.min || 10),
        description: day.weather?.[0]?.description || 'N/A',
        icon: day.weather?.[0]?.icon || '01d',
        humidity: day.humidity || 50,
        windSpeed: day.wind_speed || 0,
        windDirection: day.wind_deg || 0,
        uvIndex: day.uvi || 0,
        cloudiness: day.clouds || 0,
        sunrise: day.sunrise ? day.sunrise * 1000 : Date.now(),
        sunset: day.sunset ? day.sunset * 1000 : Date.now() + (12 * 60 * 60 * 1000),
        pressure: day.pressure || 1013,
        visibility: day.visibility ? day.visibility / 1000 : 10, // Convert to km, default 10km if not available
        birdWatchingConditions: this.getBirdWatchingConditionsForecast({
          temperature: Math.round((day.temp?.max || 20 + day.temp?.min || 10) / 2), // Average temp
          humidity: day.humidity || 50,
          windSpeed: day.wind_speed || 0,
          visibility: day.visibility ? day.visibility / 1000 : 10,
          cloudiness: day.clouds || 0,
          pressure: day.pressure || 1013,
        }),
        windConditionForBirds: this.getWindConditionForBirds(day.wind_speed || 0),
      }));

      return {
        current: currentWeather,
        forecast: forecast,
      };
    } catch (error: any) {
      // Silent handling of all weather errors - never throw exceptions
      if (error.name === 'AbortError') {
        console.log('Weather forecast API request timed out (normal when offline)');
      } else if (error.message?.includes('Network request failed') || error.message?.includes('TypeError')) {
        console.log('No internet connection for weather forecast data (normal when offline)');
      } else {
        console.log('Weather forecast API unavailable:', error.message || 'Unknown error');
      }
      return null;
    }
  }

  // Fallback method using basic current weather API
  private async getCurrentWeatherBasic(latitude: number, longitude: number): Promise<WeatherData | null> {
    try {
      const url = `${WEATHER_CONFIG.BASE_URL}?lat=${latitude}&lon=${longitude}&appid=${WEATHER_CONFIG.API_KEY}&units=${WEATHER_CONFIG.UNITS}&lang=${WEATHER_CONFIG.LANGUAGE}`;
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), WEATHER_CONFIG.TIMEOUT);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      return this.sanitizeWeatherData({
        temperature: Math.round(data.main?.temp || 0),
        feelsLike: Math.round(data.main?.feels_like || data.main?.temp || 0),
        description: data.weather?.[0]?.description || 'N/A',
        humidity: data.main?.humidity || 0,
        pressure: data.main?.pressure || 1013,
        pressureTrend: this.calculatePressureTrend(data.main?.pressure || 1013),
        windSpeed: data.wind?.speed || 0,
        windDirection: data.wind?.deg || 0,
        windGust: data.wind?.gust || null,
        visibility: data.visibility ? data.visibility / 1000 : 10, // Convert to km, default 10km
        cloudiness: data.clouds?.all || 0,
        icon: data.weather?.[0]?.icon || '01d',
        timestamp: Date.now(),
        sunrise: data.sys?.sunrise ? data.sys.sunrise * 1000 : Date.now(),
        sunset: data.sys?.sunset ? data.sys.sunset * 1000 : Date.now() + (12 * 60 * 60 * 1000),
        moonPhase: this.calculateMoonPhase(),
        barometricTrend: this.getBarometricTrend(data.main?.pressure || 1013),
      });
    } catch (error) {
      // Silent handling of all weather errors - never throw exceptions
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('Basic weather API request timed out (normal when offline)');
        } else if (error.message.includes('Network request failed') || error.message.includes('TypeError')) {
          console.log('No internet connection for basic weather data (normal when offline)');
        } else {
          console.log('Basic weather API unavailable:', error.message);
        }
      }
      // Return null instead of throwing - weather should never block report submission
      return null;
    }
  }

  private calculatePressureTrend(currentPressure: number): 'rising' | 'falling' | 'steady' {
    // This is a simplified calculation - in a real app you'd store historical data
    // For now, we'll use general pressure ranges
    if (currentPressure > 1020) return 'rising';
    if (currentPressure < 1000) return 'falling';
    return 'steady';
  }

  private getBarometricTrend(pressure: number): string {
    if (pressure > 1020) return 'Alta pressão (tempo estável)';
    if (pressure < 1000) return 'Baixa pressão (tempo instável)';
    return 'Pressão normal';
  }

  private calculateMoonPhase(): number {
    // Simplified moon phase calculation
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    
    // Known new moon date (approximate)
    const knownNewMoon = new Date(2024, 0, 11); // January 11, 2024
    const daysSinceKnownNewMoon = Math.floor((now.getTime() - knownNewMoon.getTime()) / (1000 * 60 * 60 * 24));
    const lunarCycle = 29.53; // Average lunar cycle in days
    
    const phase = (daysSinceKnownNewMoon % lunarCycle) / lunarCycle;
    return phase;
  }

  getMoonPhaseDescription(phase: number): string {
    if (phase < 0.125) return 'Lua Nova';
    if (phase < 0.25) return 'Lua Crescente';
    if (phase < 0.375) return 'Quarto Crescente';
    if (phase < 0.625) return 'Lua Cheia';
    if (phase < 0.75) return 'Quarto Minguante';
    if (phase < 0.875) return 'Lua Minguante';
    return 'Lua Nova';
  }

  getWeatherIconUrl(icon: string): string {
    return `${WEATHER_CONFIG.ICON_BASE_URL}${icon}@2x.png`;
  }

  getWindDirectionText(degrees: number): string {
    const directions = [
      'N', 'NNE', 'NE', 'ENE',
      'E', 'ESE', 'SE', 'SSE',
      'S', 'SSW', 'SW', 'WSW',
      'W', 'WNW', 'NW', 'NNW'
    ];
    
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  }

  getWindConditionForBirds(windSpeed: number): string {
    if (windSpeed < 1) return 'Calmo (ideal para observação)';
    if (windSpeed < 3) return 'Brisa leve (bom para observação)';
    if (windSpeed < 6) return 'Brisa moderada';
    if (windSpeed < 10) return 'Vento moderado (pode afetar aves pequenas)';
    if (windSpeed < 15) return 'Vento forte (aves podem procurar abrigo)';
    return 'Vento muito forte (atividade reduzida)';
  }

  getWindConditionForBirdsWithTime(windSpeed: number, isNighttime: boolean): string {
    if (isNighttime) {
      return 'Período noturno - aves em repouso';
    }
    return this.getWindConditionForBirds(windSpeed);
  }

  getUVIndexDescription(uvIndex: number): string {
    if (uvIndex <= 2) return 'Baixo';
    if (uvIndex <= 5) return 'Moderado';
    if (uvIndex <= 7) return 'Alto';
    if (uvIndex <= 10) return 'Muito Alto';
    return 'Extremo';
  }

  getBirdWatchingConditions(weather: WeatherData): string {
    const now = Date.now();
    
    // Check if it's nighttime (between sunset and sunrise)
    const isNighttime = now < weather.sunrise || now > weather.sunset;
    
    if (isNighttime) {
      const sunrise = new Date(weather.sunrise);
      const sunriseTime = sunrise.toLocaleTimeString('pt-PT', {
        hour: '2-digit',
        minute: '2-digit'
      });
      return `Período noturno - aves não ativas. Próxima observação recomendada após ${sunriseTime}`;
    }
    
    const conditions = [];
    
    // Temperature assessment
    if (weather.temperature >= 15 && weather.temperature <= 25) {
      conditions.push('Temperatura ideal');
    } else if (weather.temperature < 5 || weather.temperature > 35) {
      conditions.push('Temperatura extrema');
    }
    
    // Wind assessment
    if (weather.windSpeed < 3) {
      conditions.push('Vento calmo');
    } else if (weather.windSpeed > 10) {
      conditions.push('Vento forte');
    }
    
    // Visibility assessment
    if (weather.visibility > 8) {
      conditions.push('Excelente visibilidade');
    } else if (weather.visibility < 3) {
      conditions.push('Visibilidade reduzida');
    }
    
    // Cloud assessment
    if (weather.cloudiness < 30) {
      conditions.push('Céu limpo');
    } else if (weather.cloudiness > 80) {
      conditions.push('Muito nublado');
    }
    
    // Pressure assessment
    if (weather.pressureTrend === 'rising') {
      conditions.push('Pressão a subir (tempo a melhorar)');
    } else if (weather.pressureTrend === 'falling') {
      conditions.push('Pressão a descer (tempo a piorar)');
    }
    
    return conditions.length > 0 ? conditions.join(', ') : 'Condições normais';
  }

  getBirdWatchingConditionsForecast(data: {
    temperature: number;
    humidity: number;
    windSpeed: number;
    visibility: number;
    cloudiness: number;
    pressure: number;
  }): string {
    const conditions = [];
    
    // Temperature assessment
    if (data.temperature >= 15 && data.temperature <= 25) {
      conditions.push('Temperatura ideal');
    } else if (data.temperature < 5 || data.temperature > 35) {
      conditions.push('Temperatura extrema');
    }
    
    // Wind assessment
    if (data.windSpeed < 3) {
      conditions.push('Vento calmo');
    } else if (data.windSpeed > 10) {
      conditions.push('Vento forte');
    }
    
    // Visibility assessment
    if (data.visibility > 8) {
      conditions.push('Excelente visibilidade');
    } else if (data.visibility < 3) {
      conditions.push('Visibilidade reduzida');
    }
    
    // Cloud assessment
    if (data.cloudiness < 30) {
      conditions.push('Céu limpo');
    } else if (data.cloudiness > 80) {
      conditions.push('Muito nublado');
    }
    
    // Simplified pressure assessment for forecast
    if (data.pressure > 1020) {
      conditions.push('Alta pressão (tempo estável)');
    } else if (data.pressure < 1000) {
      conditions.push('Baixa pressão (tempo instável)');
    }
    
    return conditions.length > 0 ? conditions.join(', ') : 'Condições normais';
  }

  formatTimeFromTimestamp(timestamp: number): string {
    return new Date(timestamp).toLocaleTimeString('pt-PT', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatWeatherDescription(weather: WeatherData): string {
    return `${weather.temperature}°C, ${weather.description}`;
  }

  formatDetailedWeather(weather: WeatherData): string {
    const windDir = this.getWindDirectionText(weather.windDirection);
    const windCondition = this.getWindConditionForBirds(weather.windSpeed);
    const moonPhase = weather.moonPhase ? this.getMoonPhaseDescription(weather.moonPhase) : '';
    const uvDescription = weather.uvIndex ? this.getUVIndexDescription(weather.uvIndex) : '';
    
    const details = [
      `Temperatura: ${weather.temperature}°C (sensação: ${weather.feelsLike}°C)`,
      `Condições: ${weather.description}`,
      `Humidade: ${weather.humidity}%`,
      `Pressão: ${weather.pressure} hPa${weather.barometricTrend ? ` (${weather.barometricTrend})` : ''}`,
      `Vento: ${weather.windSpeed} m/s ${windDir}${weather.windGust ? ` (rajadas: ${weather.windGust} m/s)` : ''}`,
      `Condição do vento: ${windCondition}`,
      `Visibilidade: ${weather.visibility} km`,
      `Nebulosidade: ${weather.cloudiness}%`,
    ];
    
    if (weather.uvIndex !== undefined) {
      details.push(`Índice UV: ${weather.uvIndex} (${uvDescription})`);
    }
    
    if (weather.dewPoint !== undefined) {
      details.push(`Ponto de orvalho: ${weather.dewPoint}°C`);
    }
    
    details.push(`Nascer do sol: ${this.formatTimeFromTimestamp(weather.sunrise)}`);
    details.push(`Pôr do sol: ${this.formatTimeFromTimestamp(weather.sunset)}`);
    
    if (moonPhase) {
      details.push(`Fase lunar: ${moonPhase}`);
    }
    
    details.push(`Condições para observação: ${this.getBirdWatchingConditions(weather)}`);
    
    return details.join('\n');
  }

  isConfigured(): boolean {
    return isWeatherConfigured();
  }

  private getDefaultWeatherData(): WeatherData {
    // Implement the logic to return a default WeatherData object
    // This is a placeholder and should be replaced with the actual implementation
    return {
      temperature: 0,
      feelsLike: 0,
      description: 'N/A',
      humidity: 0,
      pressure: 1013,
      pressureTrend: 'steady',
      windSpeed: 0,
      windDirection: 0,
      windGust: null,
      visibility: 10,
      cloudiness: 0,
      uvIndex: 0,
      dewPoint: null,
      icon: '01d',
      timestamp: Date.now(),
      sunrise: Date.now(),
      sunset: Date.now() + (12 * 60 * 60 * 1000),
      moonPhase: 0.5,
      barometricTrend: 'Pressão normal',
    };
  }

  private sanitizeWeatherData(weather: any): WeatherData {
    // Remove undefined values and provide safe defaults
    const sanitized: WeatherData = {
      temperature: Math.round(weather.temperature || 0),
      feelsLike: Math.round(weather.feelsLike || weather.temperature || 0),
      description: weather.description || 'N/A',
      humidity: Math.max(0, Math.min(100, weather.humidity || 0)),
      pressure: Math.max(950, Math.min(1050, weather.pressure || 1013)),
      windSpeed: Math.max(0, Math.min(50, weather.windSpeed || 0)),
      windDirection: Math.max(0, Math.min(360, weather.windDirection || 0)),
      visibility: Math.max(0, Math.min(50, weather.visibility || 10)),
      cloudiness: Math.max(0, Math.min(100, weather.cloudiness || 0)),
      icon: weather.icon || '01d',
      timestamp: weather.timestamp || Date.now(),
      sunrise: weather.sunrise || Date.now(),
      sunset: weather.sunset || (Date.now() + (12 * 60 * 60 * 1000)),
      moonPhase: weather.moonPhase || 0.5,
      barometricTrend: weather.barometricTrend || 'Pressão normal',
    };

    // Add optional fields only if they have valid values
    if (weather.pressureTrend && ['rising', 'falling', 'steady'].includes(weather.pressureTrend)) {
      sanitized.pressureTrend = weather.pressureTrend;
    }

    if (weather.windGust && typeof weather.windGust === 'number' && weather.windGust > 0) {
      sanitized.windGust = Math.max(0, Math.min(100, weather.windGust));
    }

    if (weather.uvIndex && typeof weather.uvIndex === 'number' && weather.uvIndex >= 0) {
      sanitized.uvIndex = Math.max(0, Math.min(15, weather.uvIndex));
    }

    if (weather.dewPoint && typeof weather.dewPoint === 'number') {
      sanitized.dewPoint = Math.max(-50, Math.min(50, weather.dewPoint));
    }

    return sanitized;
  }

  // Static utility method for sanitizing weather data
  static sanitizeWeatherForFirestore(weather: any): WeatherConditions | null {
    if (!weather) {
      console.log('No weather data provided to sanitize');
      return null;
    }

    try {
      const sanitized: WeatherConditions = {
        temperature: Math.round(weather.temperature || 0),
        feelsLike: Math.round(weather.feelsLike || weather.temperature || 0),
        description: weather.description || 'N/A',
        humidity: Math.max(0, Math.min(100, weather.humidity || 0)),
        pressure: Math.max(950, Math.min(1050, weather.pressure || 1013)),
        windSpeed: Math.max(0, Math.min(50, weather.windSpeed || 0)),
        windDirection: Math.max(0, Math.min(360, weather.windDirection || 0)),
        visibility: Math.max(0, Math.min(50, weather.visibility || 10)),
        cloudiness: Math.max(0, Math.min(100, weather.cloudiness || 0)),
        icon: weather.icon || '01d',
        timestamp: weather.timestamp || Date.now(),
        sunrise: weather.sunrise || Date.now(),
        sunset: weather.sunset || (Date.now() + (12 * 60 * 60 * 1000)),
        moonPhase: weather.moonPhase || 0.5,
        barometricTrend: weather.barometricTrend || 'Pressão normal',
      };

      // Add optional fields only if they have valid values
      if (weather.pressureTrend && ['rising', 'falling', 'steady'].includes(weather.pressureTrend)) {
        sanitized.pressureTrend = weather.pressureTrend;
      }

      if (weather.windGust && typeof weather.windGust === 'number' && weather.windGust > 0) {
        sanitized.windGust = Math.max(0, Math.min(100, weather.windGust));
      }

      if (weather.uvIndex && typeof weather.uvIndex === 'number' && weather.uvIndex >= 0) {
        sanitized.uvIndex = Math.max(0, Math.min(15, weather.uvIndex));
      }

      if (weather.dewPoint && typeof weather.dewPoint === 'number') {
        sanitized.dewPoint = Math.max(-50, Math.min(50, weather.dewPoint));
      }

      // Validate that no fields are undefined
      const cleanedSanitized: any = {};
      Object.keys(sanitized).forEach(key => {
        const value = (sanitized as any)[key];
        if (value !== undefined && value !== null) {
          cleanedSanitized[key] = value;
        }
      });

      return cleanedSanitized as WeatherConditions;
    } catch (error) {
      console.error('Error sanitizing weather data:', error);
      return null;
    }
  }
}

export { WeatherService };
export const weatherService = new WeatherService(); 