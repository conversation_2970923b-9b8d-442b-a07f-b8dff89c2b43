import React, { useState, useEffect, useRef, useCallback } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Alert, ActivityIndicator, Platform, Animated, Modal, Dimensions, Image, BackHandler, AppState, AppStateStatus } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import SafeSystemBars from '@/components/SafeSystemBars';
import * as Location from 'expo-location';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/contexts/AuthContext';
import { getFirestore, collection, addDoc, serverTimestamp, query, where, orderBy, getDocs, Timestamp, deleteDoc, doc, onSnapshot, DocumentData, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from '@/config/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import MapView, { Marker, MapMarker, PROVIDER_GOOGLE, Callout, Polyline } from 'react-native-maps';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import { PendingReport } from '@/types/reports';
import { TecnicoProtocol } from '@/types/reports';
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';
import { useFocusEffect } from '@react-navigation/native';
import { useNetwork } from '@/contexts/NetworkContext';
import Svg, { Path } from 'react-native-svg';
import ContactPlacementModal from '@/components/ContactPlacementModal';
import { monitoringSyncService } from '@/services/monitoringSyncService';
import { useRouter } from 'expo-router';
import { 
  calculateDistance, 
  calculateSpeed, 
  shouldRecordGPSPoint, 
  shouldUpdateBearing, 
  createEnhancedGPSPoint,
  analyzeGPSSpike,
  calculateAccuracyGate,
  DEFAULT_FILTER_CONFIG 
} from '@/utils/locationFiltering';
import KeepAwake from 'react-native-keep-awake';

// Portuguese translations
const pt = {
  monitoringActive: 'Monitorização Ativa',
  terminate: 'Terminar',
  confirmTerminate: 'Deseja terminar a monitorização?',
  yes: 'Sim',
  no: 'Não',
  timeElapsed: 'Duração:',
  distanceTraveled: 'Distância:',
  contactsCount: 'Contactos:',
  protocol: 'Protocolo',
  locationError: 'Erro ao obter localização',
  permissionDenied: 'Permissão de localização negada',
  gpsUnavailable: 'GPS indisponível',
  retryLocation: 'Tentar Novamente',
  gpsAccuracy: 'Precisão GPS:',
  gpsAccuracyHelp: 'Raio de incerteza da sua posição. Valores baixos (≤10m) são melhores',
  gpsAccuracyExplanation: 'A sua posição real pode estar num raio de',
  protocolOptions: {
    trajeto: 'Trajeto',
    estacoes_escuta: 'Estações de escuta',
    metodo_mapas: 'Método dos mapas',
    contagens_pontos: 'Contagens em pontos específicos',
    captura_marcacao: 'Captura e marcação de indivíduos',
    acompanhamento_cacadas: 'Acompanhamento das caçadas',
    registos_ocasionais: 'Registos ocasionais',
  },
  toggle3D: 'Alternar vista 3D/2D',
};

type LocationType = Location.LocationObject | null;

// Default region (Portugal center) - very close zoom for walking
const DEFAULT_REGION = {
  latitude: 39.5,
  longitude: -8.0,
  latitudeDelta: 0.001, // Very close zoom for precise monitoring
  longitudeDelta: 0.001,
};

// Trajectory data interface
interface TrajectoryData {
  id: string;
  name: string;
  description?: string;
  coordinates: Array<{ lat: number; lng: number }>;
  distance?: string;
  pointsCount?: number;
  createdAt: string;
  source: 'kmz' | 'manual';
  originalFileName?: string;
}

interface ActiveMonitoringProps {
  protocol: TecnicoProtocol;
  startTime: Date;
  weatherData?: any;
  observersCount?: number;
  selectedTrajectory?: TrajectoryData | null;
  onTerminate: () => void;
}

// Import the dove icon statically
const doveIconPng = require('../assets/images/icons/dove-icon-3x.png');

// Smart Dove Icon Component - FontAwesome6 when online, SVG dove when offline
const DoveIcon = ({ size = 20, color = "#FFFFFF", style }: { size?: number; color?: string; style?: any }) => {
  const { hasInternetConnection } = useNetwork();
  const isOnline = hasInternetConnection();
  const [svgError, setSvgError] = useState(false);
  
  if (isOnline) {
    // Use FontAwesome6 dove when online
    return <FontAwesome6 name="dove" size={size} color={color} style={style} />;
  } else {
    // Use exact FontAwesome SVG dove when offline
    
    if (svgError) {
      // Fallback to FontAwesome if SVG fails
      return <FontAwesome name="send" size={size} color={color} style={style} />;
    }
    
    try {
      // Scale up the SVG to match FontAwesome6 size
      const svgSize = size * 1.2; // Increase by 20% to match FontAwesome6
      
      return (
        <View style={[
          { 
            width: size, 
            height: size, 
            justifyContent: 'center', 
            alignItems: 'center',
          }, 
          style
        ]}>
          <Svg
            width={svgSize}
            height={svgSize}
            viewBox="0 0 576 512"
            style={{ 
              width: svgSize, 
              height: svgSize,
            }}
          >
            <Path 
              d="M160.8 96.5c14 17 31 30.9 49.5 42.2c25.9 15.8 53.7 25.9 77.7 31.6l0-31.5C265.8 108.5 250 71.5 248.6 28c-.4-11.3-7.5-21.5-18.4-24.4c-7.6-2-15.8-.2-21 5.8c-13.3 15.4-32.7 44.6-48.4 87.2zM320 144l0 30.6s0 0 0 0l0 1.3s0 0 0 0l0 32.1c-60.8-5.1-185-43.8-219.3-157.2C97.4 40 87.9 32 76.6 32c-7.9 0-15.3 3.9-18.8 11C46.8 65.9 32 112.1 32 176c0 116.9 80.1 180.5 118.4 202.8L11.8 416.6C6.7 418 2.6 421.8 .9 426.8s-.8 10.6 2.3 14.8C21.7 466.2 77.3 512 160 512c3.6 0 7.2-1.2 10-3.5L245.6 448l74.4 0c88.4 0 160-71.6 160-160l0-160 29.9-44.9c1.3-2 2.1-4.4 2.1-6.8c0-6.8-5.5-12.3-12.3-12.3L400 64c-44.2 0-80 35.8-80 80zm80-16a16 16 0 1 1 0 32 16 16 0 1 1 0-32z" 
              fill={color}
              stroke={color}
              strokeWidth="0"
            />
          </Svg>
        </View>
      );
    } catch (error) {
      console.error('🚨 SVG dove icon error:', error);
      setSvgError(true);
      return <FontAwesome name="send" size={size} color={color} style={style} />;
    }
  }
};

export default function ActiveMonitoring({ protocol, startTime, weatherData, observersCount, selectedTrajectory, onTerminate }: ActiveMonitoringProps) {
  const colorScheme = useColorScheme();
  const { user, userRole } = useAuth();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  const markerRef = useRef<MapMarker>(null);
  const [location, setLocation] = useState<LocationType>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [mapReady, setMapReady] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [mapFullyLoaded, setMapFullyLoaded] = useState(false);
  const [hasInitialAnimation, setHasInitialAnimation] = useState(false);
  const [currentRegion, setCurrentRegion] = useState({
    latitudeDelta: 0.0005, // Starting with maximum zoom for precise contact placement
    longitudeDelta: 0.0005
  });
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const [mapType, setMapType] = useState<'standard' | 'satellite'>('standard');
  const [currentZoomLevel, setCurrentZoomLevel] = useState<number>(20);
  const [is3DView, setIs3DView] = useState(false); // Start with 2D view
  const [compassTracking, setCompassTracking] = useState(false); // Compass tracking mode
  const [compassActive, setCompassActive] = useState(false); // Whether compass is actually working
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Time counter state
  const [elapsedTime, setElapsedTime] = useState(0);
  const [showTerminateModal, setShowTerminateModal] = useState(false);
  const [finalElapsedTime, setFinalElapsedTime] = useState(0);
  const [sessionEndTime, setSessionEndTime] = useState<Date | null>(null);

  // Add missing state declarations
  const [showSummaryModal, setShowSummaryModal] = useState(false);
  const [isSendingData, setIsSendingData] = useState(false);

  // Distance tracking state
  const [totalDistance, setTotalDistance] = useState(0);
  const [currentSpeed, setCurrentSpeed] = useState(0); // Current speed in m/s // in meters
  const [pathCoordinates, setPathCoordinates] = useState<{latitude: number, longitude: number}[]>([]);
  
  // Monitoring start location - captured when user presses "Iniciar Monitorização"
  const [monitoringStartLocation, setMonitoringStartLocation] = useState<{latitude: number, longitude: number} | null>(null);

  // Contacts tracking state
  const [contactsCount, setContactsCount] = useState(0);

  // Trajectory display state
  const [trajectoryCoordinates, setTrajectoryCoordinates] = useState<{latitude: number, longitude: number}[]>([]);

  // Contact markers state
  const [contactMarkers, setContactMarkers] = useState<Array<{
    id: string;
    coordinate: { latitude: number; longitude: number };
    observerLocation: { latitude: number; longitude: number };
    contactNumber: number;
    timestamp: string;
  }>>([]);

  // Convert trajectory coordinates on mount
  useEffect(() => {
    if (selectedTrajectory && selectedTrajectory.coordinates) {
      const convertedCoordinates = selectedTrajectory.coordinates.map(coord => ({
        latitude: coord.lat,
        longitude: coord.lng
      }));
      setTrajectoryCoordinates(convertedCoordinates);
      
      // If we have trajectory coordinates, fit the map to show them initially
      if (convertedCoordinates.length > 0) {
        setTimeout(() => {
          if (mapRef.current) {
            mapRef.current.fitToCoordinates(convertedCoordinates, {
              edgePadding: { top: 100, right: 100, bottom: 100, left: 100 },
              animated: true,
            });
          }
        }, 3000);
      }
    } else {
      setTrajectoryCoordinates([]);
    }
  }, [selectedTrajectory]);

  // Contact placement modal state
  const [showContactModal, setShowContactModal] = useState(false);
  const [currentContactData, setCurrentContactData] = useState<{
    observerLocation: {latitude: number, longitude: number};
    contactLocation: {latitude: number, longitude: number};
    distance: number;
    bearing: number;
  } | null>(null);

  // Contact upload progress
  const [isUploadingContactImages, setIsUploadingContactImages] = useState(false);
  const [contactUploadProgress, setContactUploadProgress] = useState(0);
  const [uploadMessage, setUploadMessage] = useState('');






  
  // Emergency force-close function
  const forceCloseAllModals = useCallback(() => {
    console.log('🚨 EMERGENCY: Force closing all modals');
    setIsUploadingContactImages(false);
  }, []);

  // Emergency reset on component mount to clear any stuck state
  useEffect(() => {
    console.log('🚨 Component mounted - clearing any stuck modal state');
    forceCloseAllModals();
  }, [forceCloseAllModals]);



  // Offline monitoring data storage
  const [monitoringSessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  // Network monitoring effect - sync data when internet becomes available
  useEffect(() => {
    // Set up progress callback for image sync
    monitoringSyncService.setProgressCallback((progress: number, message: string) => {
      if (progress > 0) {
        setIsUploadingContactImages(true);
        setContactUploadProgress(progress);
        setUploadMessage(message);
      } else {
        setIsUploadingContactImages(false);
        setContactUploadProgress(0);
        setUploadMessage('');
      }
    });

    const unsubscribe = NetInfo.addEventListener(async (state) => {
      // Log network changes but don't auto-sync during active monitoring to prevent stuck modal
      if (state.isConnected && state.isInternetReachable) {
        console.log('🌐 Internet connection restored - sync will happen after monitoring ends');
      }
    });

    return () => {
      unsubscribe();
      monitoringSyncService.setProgressCallback(null);
    };
  }, []);

  // Load persisted monitoring data on component mount
  useEffect(() => {
    const loadMonitoringData = async () => {
      try {
        const savedData = await AsyncStorage.getItem('monitoringData');
        if (savedData) {
          const data = JSON.parse(savedData);
          
          setTotalDistance(data.totalDistance || 0);
          setContactsCount(data.contactsCount || 0);
          setPathCoordinates(data.pathCoordinates || []);
        }

        // Load existing contact events for this session
        await loadContactMarkers();
      } catch (error) {
        console.error('Error loading monitoring data:', error);
      }
    };

    loadMonitoringData();
  }, []);

  // Load contact markers for current session
  const loadContactMarkers = async () => {
    try {
      const contactEvents = await AsyncStorage.getItem('offlineContactEvents');
      if (contactEvents) {
        const events = JSON.parse(contactEvents);
        const sessionEvents = events.filter((event: any) => event.sessionId === monitoringSessionId);
        
        const markers = sessionEvents.map((event: any) => ({
          id: `${event.sessionId}_${event.contactNumber}`,
          coordinate: event.contactLocation,
          observerLocation: event.observerLocation,
          contactNumber: event.contactNumber,
          timestamp: event.timestamp,
        }));
        
        setContactMarkers(markers);
        setContactsCount(sessionEvents.length);
        console.log(`📍 Loaded ${markers.length} contact markers for session`);
      }
    } catch (error) {
      console.error('Error loading contact markers:', error);
    }
  };

  // Create current monitoring session for contact-details screen
  useEffect(() => {
    const createCurrentMonitoringSession = async () => {
      try {
        const currentSession = {
          sessionId: monitoringSessionId,
          protocol,
          startTime: startTime.toISOString(),
          userId: user?.uid,
          deviceInfo: {
            platform: Platform.OS,
          }
        };
        
        await AsyncStorage.setItem('currentMonitoringSession', JSON.stringify(currentSession));
        console.log('✅ Current monitoring session created for contact-details');
      } catch (error) {
        console.error('Error creating current monitoring session:', error);
      }
    };

    createCurrentMonitoringSession();
  }, [monitoringSessionId, protocol, startTime, user?.uid]);

  // Save comprehensive monitoring data whenever it changes (debounced)
  useEffect(() => {
    // Debounce saving to reduce memory pressure
    const timeoutId = setTimeout(async () => {
      const saveMonitoringData = async () => {
        try {
          const dataToSave = {
            sessionId: monitoringSessionId,
            protocol,
            startTime: startTime.toISOString(),
            totalDistance,
            contactsCount,
            pathCoordinates, // Save ALL GPS points, not just last 100
            monitoringStartLocation, // Save the actual monitoring start location
            lastUpdated: new Date().toISOString(),
            userId: user?.uid,
            status: 'active', // Add required status field
            deviceInfo: {
              platform: Platform.OS,
            }
          };
          
          await AsyncStorage.setItem('monitoringData', JSON.stringify(dataToSave));
          
          // Also save to a separate offline monitoring sessions array for later sync
          await saveOfflineMonitoringSession(dataToSave);
        } catch (error) {
          console.error('Error saving monitoring data:', error);
        }
      };

      // Only save if we have some data to avoid saving initial empty state
      if (totalDistance > 0 || contactsCount > 0 || pathCoordinates.length > 0) {
        saveMonitoringData();
      }
    }, 2000); // Debounce by 2 seconds

    return () => clearTimeout(timeoutId);
  }, [totalDistance, contactsCount, pathCoordinates, protocol, startTime, user?.uid, monitoringSessionId]);

  // Save offline monitoring session for later sync
  const saveOfflineMonitoringSession = async (sessionData: any) => {
    try {
      const offlineSessions = await AsyncStorage.getItem('offlineMonitoringSessions');
      const sessions = offlineSessions ? JSON.parse(offlineSessions) : [];
      
      // Update existing session or add new one
      const existingIndex = sessions.findIndex((s: any) => s.sessionId === sessionData.sessionId);
      if (existingIndex >= 0) {
        sessions[existingIndex] = sessionData;
      } else {
        sessions.push(sessionData);
      }
      
      await AsyncStorage.setItem('offlineMonitoringSessions', JSON.stringify(sessions));
    } catch (error) {
      console.error('Error saving offline monitoring session:', error);
    }
  };

  // Save individual GPS points for detailed tracking
  const saveGPSPoint = async (location: Location.LocationObject, distance?: number) => {
    try {
      const gpsPoint = {
        sessionId: monitoringSessionId,
        timestamp: new Date().toISOString(),
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude,
        heading: location.coords.heading,
        speed: location.coords.speed,
        distance: distance || 0,
      };

      // Save to GPS points array
      const gpsPoints = await AsyncStorage.getItem('offlineGPSPoints');
      const points = gpsPoints ? JSON.parse(gpsPoints) : [];
      points.push(gpsPoint);
      
      // Keep only last 500 points to reduce memory usage
      if (points.length > 500) {
        points.splice(0, points.length - 500);
      }
      
      await AsyncStorage.setItem('offlineGPSPoints', JSON.stringify(points));
    } catch (error) {
      console.error('Error saving GPS point:', error);
    }
  };

  // Save contact events
  const saveContactEvent = async (observerLocation: {latitude: number, longitude: number}, contactLocation: {latitude: number, longitude: number}, distance: number) => {
    try {
      const contactEvent = {
        sessionId: monitoringSessionId,
        timestamp: new Date().toISOString(),
        observerLocation: observerLocation,
        contactLocation: contactLocation,
        distance: distance,
        contactNumber: contactsCount + 1,
      };

      const contactEvents = await AsyncStorage.getItem('offlineContactEvents');
      const events = contactEvents ? JSON.parse(contactEvents) : [];
      events.push(contactEvent);
      
      await AsyncStorage.setItem('offlineContactEvents', JSON.stringify(events));
    } catch (error) {
      console.error('Error saving contact event:', error);
    }
  };

  // New state for location retry
  const [isRetryingLocation, setIsRetryingLocation] = useState(false);

  // Screen dimensions for responsive header
  const [screenData, setScreenData] = useState(Dimensions.get('window'));
  const isLandscape = screenData.width > screenData.height;

  // Heading smoothing for better rotation
  const currentHeading = useRef<number>(0);
  const lastUpdateTime = useRef<number>(0);
  const lastPosition = useRef<{lat: number, lng: number} | null>(null);
  const stationaryCount = useRef<number>(0);
  const userInteracting = useRef<boolean>(false);
  const interactionTimeout = useRef<NodeJS.Timeout | null>(null);
  const regionChangeTimeout = useRef<NodeJS.Timeout | null>(null);
  const locationUpdating = useRef<boolean>(false);
  const currentCamera = useRef<{
    center: { latitude: number; longitude: number };
    zoom: number;
    pitch: number;
    heading: number;
  } | null>(null);
  const lastLoggedHeading = useRef<number>(-1);
  const lastLoggedZoom = useRef<number>(-1);
  const locationSubscription = useRef<Location.LocationSubscription | undefined>(undefined);
  const sensorsStarted = useRef<boolean>(false);
  const lastBearing = useRef<number | null>(null);
  const stationaryTime = useRef<number>(0);
  // Enhanced movement detection using adaptive filtering
  const lastRecordedTime = useRef<number>(0);

  // GPS accuracy tracking state
  const [gpsAccuracy, setGpsAccuracy] = useState<number | null>(null);
  const [gpsAccuracyStatus, setGpsAccuracyStatus] = useState<'excellent' | 'good' | 'fair' | 'poor'>('good');
  const [renderKey, setRenderKey] = useState(0); // Force re-render key
  const lastAccuracy = useRef<number | null>(null); // Track previous accuracy for gating

  // GPS accuracy classification function
  const classifyGpsAccuracy = (accuracy: number | null): 'excellent' | 'good' | 'fair' | 'poor' => {
    if (!accuracy) return 'poor';
    if (accuracy <= 5) return 'excellent';
    if (accuracy <= 10) return 'good';
    if (accuracy <= 20) return 'fair';
    return 'poor';
  };

  // Get GPS accuracy color
  const getGpsAccuracyColor = (status: string): string => {
    switch (status) {
      case 'excellent': return '#10B981'; // Green
      case 'good': return '#3B82F6'; // Blue
      case 'fair': return '#F59E0B'; // Yellow/Orange
      case 'poor': return '#EF4444'; // Red
      default: return '#6B7280'; // Gray
    }
  };

  // Get GPS accuracy text
  const getGpsAccuracyText = (status: string): string => {
    switch (status) {
      case 'excellent': return 'GPS Excelente';
      case 'good': return 'GPS Bom';
      case 'fair': return 'GPS Razoável';
      case 'poor': return 'GPS Fraco - Impreciso';
      default: return 'GPS Desconhecido';
    }
  };



  // Calculate optimal zoom level based on current speed
  const getOptimalZoomLevel = (speedMs: number): number => {
    const speedKmh = speedMs * 3.6; // Convert m/s to km/h
    
    if (speedKmh <= 2) {
      // Walking speed (0-2 km/h) - Maximum detail for precise contact placement
      return 20;
    } else if (speedKmh <= 6) {
      // Fast walking/jogging (2-6 km/h) - High detail
      return 19;
    } else if (speedKmh <= 15) {
      // Cycling speed (6-15 km/h) - Medium detail
      return 18;
    } else if (speedKmh <= 30) {
      // Fast cycling/slow car (15-30 km/h) - Lower detail
      return 17;
    } else if (speedKmh <= 60) {
      // Car speed (30-60 km/h) - Wide view
      return 16;
    } else {
      // High speed (60+ km/h) - Very wide view
      return 15;
    }
  };

  // Calculate optimal region delta based on speed
  const getOptimalRegionDelta = (speedMs: number): number => {
    const speedKmh = speedMs * 3.6;
    
    if (speedKmh <= 2) {
      return 0.0005; // Maximum precision for walking
    } else if (speedKmh <= 6) {
      return 0.0008; // High precision for fast walking
    } else if (speedKmh <= 15) {
      return 0.002; // Medium precision for cycling
    } else if (speedKmh <= 30) {
      return 0.005; // Lower precision for fast cycling
    } else if (speedKmh <= 60) {
      return 0.01; // Wide view for car
    } else {
      return 0.02; // Very wide view for high speed
    }
  };

  useEffect(() => {
    const onChange = (result: { window: any }) => {
      setScreenData(result.window);
    };
    
    const subscription = Dimensions.addEventListener('change', onChange);
    return () => subscription?.remove();
  }, []);

  // Time counter effect
  useEffect(() => {
    const interval = setInterval(() => {
      // Stop counting if session has been terminated
      if (sessionEndTime) return;
      
      const now = new Date();
      const elapsed = Math.floor((now.getTime() - startTime.getTime()) / 1000);
      setElapsedTime(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime, sessionEndTime]);

  // Calculate bearing between two points
  const calculateBearing = (startLat: number, startLng: number, destLat: number, destLng: number): number => {
    const dLng = (destLng - startLng) * (Math.PI / 180);
    const startLatRad = startLat * (Math.PI / 180);
    const destLatRad = destLat * (Math.PI / 180);
    
    const y = Math.sin(dLng) * Math.cos(destLatRad);
    const x = Math.cos(startLatRad) * Math.sin(destLatRad) - 
              Math.sin(startLatRad) * Math.cos(destLatRad) * Math.cos(dLng);
    
    let bearing = Math.atan2(y, x) * (180 / Math.PI);
    return (bearing + 360) % 360; // Normalize to 0-360
  };

  // Using calculateDistance from utils/locationFiltering.ts

  // Smooth bearing changes to avoid jittery rotation
  const smoothBearing = (newBearing: number, lastBearing: number | null): number => {
    if (lastBearing === null) return newBearing;
    
    // Handle the 0-360 wrap around
    let diff = newBearing - lastBearing;
    if (diff > 180) diff -= 360;
    if (diff < -180) diff += 360;
    
    // Only apply significant bearing changes to avoid jitter
    if (Math.abs(diff) < 10) return lastBearing;
    
    // Smooth the bearing change
    const smoothed = lastBearing + (diff * 0.3); // 30% of the change
    return (smoothed + 360) % 360;
  };

  // Screen wake state tracking
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);
  const [isScreenAwake, setIsScreenAwake] = useState(true);
  const lastGoodAccuracy = useRef<number | null>(null);
  const screenWakeTime = useRef<number>(Date.now());

  // Keep screen awake during monitoring
  useEffect(() => {
    try {
      KeepAwake.activate();
      console.log('📱 Screen timeout prevention activated');
    } catch (error) {
      console.error('❌ Failed to activate keep awake:', error);
    }

    // Cleanup on unmount
    return () => {
      try {
        KeepAwake.deactivate();
        console.log('📱 Screen timeout prevention deactivated');
      } catch (error) {
        console.error('❌ Failed to deactivate keep awake:', error);
      }
    };
  }, []);



  // Monitor app state changes to detect screen wake/sleep
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log('📱 App state changed:', appState, '->', nextAppState);
      
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        // Screen woke up - GPS might need time to re-acquire accuracy
        console.log('📱 Screen woke up - GPS re-acquisition period started');
        screenWakeTime.current = Date.now();
        setIsScreenAwake(true);
        

      } else if (nextAppState.match(/inactive|background/)) {
        // Screen went to sleep
        console.log('📱 Screen went to sleep');
        setIsScreenAwake(false);
      }
      
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [appState]);

  // Location and bearing tracking effect
  useEffect(() => {
    const startLocationTracking = async () => {
      if (sensorsStarted.current) {
        return;
      }

      try {
        sensorsStarted.current = true;
        
        // Capture the current location as the monitoring start location
        try {
          const currentLocation = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.BestForNavigation,
          });
          const startCoordinate = {
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude
          };
          setMonitoringStartLocation(startCoordinate);
          console.log('📍 Monitoring start location captured:', startCoordinate);
        } catch (error) {
          console.log('❌ Failed to capture monitoring start location:', error);
        }
        

        
        const { status: locationStatus } = await Location.getForegroundPermissionsAsync();
        if (locationStatus === 'granted') {
          locationSubscription.current = await Location.watchPositionAsync(
            {
              accuracy: Location.Accuracy.BestForNavigation,
              timeInterval: 2000, // Reasonable update frequency (2 seconds)
              distanceInterval: 2, // Update every 2 meters of movement
              mayShowUserSettingsDialog: true,
            },
            (newLocation) => {
              const currentTime = Date.now();
              setLocation(newLocation);
              setErrorMsg(null);
              
              // Update GPS accuracy tracking
              const accuracy = newLocation.coords.accuracy;
              // Only log significant accuracy changes to reduce spam
              if (!gpsAccuracy || Math.abs(accuracy - gpsAccuracy) > 1) {
                console.log(`📍 GPS Accuracy: ${accuracy ? accuracy.toFixed(1) + 'm' : 'null'} (was ${gpsAccuracy ? gpsAccuracy.toFixed(1) + 'm' : 'null'})`);
              }
              
              // Force update accuracy state - always update to ensure freshness
              setGpsAccuracy(accuracy);
              const accuracyStatus = classifyGpsAccuracy(accuracy);
              setGpsAccuracyStatus(accuracyStatus);
              setRenderKey(k => k + 1); // Always force re-render

              // Track good accuracy readings
              if (accuracy && accuracy < 10) {
                lastGoodAccuracy.current = accuracy;
              }
              
              // Add current location to path
              const newCoordinate = {
                latitude: newLocation.coords.latitude,
                longitude: newLocation.coords.longitude
              };
              
              if (lastPosition.current) {
                const distance = calculateDistance(
                  lastPosition.current.lat,
                  lastPosition.current.lng,
                  newLocation.coords.latitude,
                  newLocation.coords.longitude
                );
                
                const timeDiff = (currentTime - lastUpdateTime.current) / 1000; // seconds
                const speed = calculateSpeed(distance, timeDiff * 1000); // m/s
                
                // Update current speed state for dynamic zoom
                setCurrentSpeed(speed);
                
                // Update region delta based on speed
                const optimalDelta = getOptimalRegionDelta(speed);
                setCurrentRegion({
                  latitudeDelta: optimalDelta,
                  longitudeDelta: optimalDelta
                });
                
                // Check if we're in post-wake GPS re-acquisition period
                const timeSinceWake = currentTime - screenWakeTime.current;
                const isPostWakeAcquisition = timeSinceWake < 30000; // 30 seconds after wake
                
                // Enhanced GPS filtering optimized for technician monitoring
                // Automatically adjust for different phone positions (silent, no user warnings)
                let adjustedFilterConfig = {
                  ...DEFAULT_FILTER_CONFIG,
                  maxAccuracy: 60, // More lenient accuracy for various phone positions
                  maxSpeed: accuracyStatus === 'poor' ? 15 : DEFAULT_FILTER_CONFIG.maxSpeed,
                  minTimeInterval: 1200, // Slightly longer intervals to account for orientation changes
                  maxJumpDistance: accuracyStatus === 'poor' ? 180 : (accuracyStatus === 'fair' ? 250 : 200),
                  maxJumpSpeed: accuracyStatus === 'poor' ? 15 : DEFAULT_FILTER_CONFIG.maxJumpSpeed,
                };

                // Silent orientation compensation - adjust thresholds based on GPS behavior patterns
                // If GPS accuracy is varying significantly, assume phone position may be suboptimal
                if (accuracy && accuracy > 30) {
                  // Likely phone is not in optimal position - be more conservative
                  adjustedFilterConfig = {
                    ...adjustedFilterConfig,
                    maxAccuracy: 70, // Allow worse accuracy
                    maxJumpDistance: 150, // Reduce jump threshold
                    minTimeInterval: 1800, // Longer intervals
                  };
                } else if (accuracy && accuracy <= 15) {
                  // Good GPS signal - likely phone in optimal position - be more permissive
                  adjustedFilterConfig = {
                    ...adjustedFilterConfig,
                    maxAccuracy: 45, // Expect better accuracy
                    maxJumpDistance: 280, // Allow larger jumps
                    minTimeInterval: 900, // More frequent updates
                  };
                }

                // Apply stricter filtering after screen wake to prevent fluctuations
                if (isPostWakeAcquisition) {
                  adjustedFilterConfig = {
                    ...adjustedFilterConfig,
                    maxAccuracy: accuracy && accuracy > 30 ? 80 : 50, // Allow poor accuracy briefly after wake
                    maxJumpDistance: 100, // Smaller jump threshold after wake
                    minTimeInterval: 3000, // Require longer intervals after wake
                  };
                  console.log(`📱 Post-wake GPS filtering: accuracy=${accuracy?.toFixed(1)}m, distance=${distance.toFixed(1)}m, timeSinceWake=${(timeSinceWake/1000).toFixed(1)}s`);
                }
                
                // Analyze for GPS spikes before recording
                const spikeAnalysis = analyzeGPSSpike(
                  distance,
                  speed,
                  newLocation.coords.accuracy,
                  timeDiff * 1000,
                  adjustedFilterConfig
                );
                
                // Log spike detection for debugging
                if (spikeAnalysis.isSpike) {
                  console.warn(`🚨 GPS Spike Detected:`, {
                    distance: `${distance.toFixed(1)}m`,
                    speed: `${speed.toFixed(1)}m/s (${(speed * 3.6).toFixed(1)}km/h)`,
                    accuracy: `${newLocation.coords.accuracy?.toFixed(1)}m`,
                    reason: spikeAnalysis.reason,
                    severity: spikeAnalysis.severity,
                    recommendation: spikeAnalysis.recommendation
                  });
                }
                
                // Use advanced accuracy-gating to prevent false movement detection
                const shouldRecord = shouldRecordGPSPoint(
                  distance,
                  speed,
                  newLocation.coords.accuracy,
                  timeDiff * 1000, // Convert to milliseconds
                  adjustedFilterConfig,
                  lastAccuracy.current // Pass previous accuracy for gating
                );
                
                if (shouldRecord) {
                  // Record significant movement
                  console.log(`✅ GPS: Recording ${distance.toFixed(1)}m movement (speed: ${speed.toFixed(2)}m/s, accuracy: ${newLocation.coords.accuracy?.toFixed(1)}m)`);
                  setTotalDistance(prevTotal => {
                    const newTotal = prevTotal + distance;
                    console.log(`📊 GPS: Total distance: ${prevTotal.toFixed(1)}m → ${newTotal.toFixed(1)}m`);
                    return newTotal;
                  });
                  

                  
                  // Add GPS point to complete trajectory path
                  setPathCoordinates(prevPath => [...prevPath, newCoordinate]);
                  
                  // Save enhanced GPS point with filtering metadata
                  const enhancedGPSPoint = createEnhancedGPSPoint(
                    monitoringSessionId,
                    newLocation,
                    distance,
                    speed,
                    true
                  );
                  saveGPSPoint(newLocation, distance);
                  
                  // Update last recorded time and accuracy
                  lastRecordedTime.current = currentTime;
                  lastAccuracy.current = newLocation.coords.accuracy;
                } else {
                  // Detailed logging is now handled by shouldRecordGPSPoint function
                  
                  // Save GPS point for debugging but don't add to path
                  const enhancedGPSPoint = createEnhancedGPSPoint(
                    monitoringSessionId,
                    newLocation,
                    distance,
                    speed,
                    false
                  );
                  saveGPSPoint(newLocation, 0);
                  // Still update accuracy for next gating calculation
                  lastAccuracy.current = newLocation.coords.accuracy;
                }
                
                // Only update bearing if moving with sufficient speed and distance
                if (shouldUpdateBearing(distance, speed)) {
                  const bearing = calculateBearing(
                    lastPosition.current.lat,
                    lastPosition.current.lng,
                    newLocation.coords.latitude,
                    newLocation.coords.longitude
                  );
                  
                  const smoothedBearing = smoothBearing(bearing, lastBearing.current);
                  lastBearing.current = smoothedBearing;
                  stationaryTime.current = 0;
                  
                  // Update map rotation only if user is not manually interacting and map is ready
                  if (!userInteracting.current && mapRef.current && mapReady) {
                    locationUpdating.current = true;
                    
                    const cameraCenter = currentCamera.current ? 
                      currentCamera.current.center : 
                      { latitude: newLocation.coords.latitude, longitude: newLocation.coords.longitude };
                    
                    const cameraZoom = currentCamera.current ? 
                      currentCamera.current.zoom : 17;
                    
                    mapRef.current.animateCamera({
                      center: {
                        latitude: newLocation.coords.latitude,
                        longitude: newLocation.coords.longitude
                      },
                      pitch: is3DView ? 45 : 0,
                      heading: smoothedBearing,
                      altitude: 500,
                      zoom: cameraZoom,
                    }, { duration: 500 });
                    
                    // Update camera state and zoom level
                    currentCamera.current = {
                      center: {
                        latitude: newLocation.coords.latitude,
                        longitude: newLocation.coords.longitude
                      },
                      zoom: cameraZoom,
                      pitch: is3DView ? 45 : 0,
                      heading: smoothedBearing
                    };
                    
                    // Update zoom level state to keep it in sync
                    setCurrentZoomLevel(cameraZoom);
                  }
                } else {
                  stationaryTime.current += timeDiff;
                  
                  // Update position but keep current heading
                  if (!userInteracting.current && mapRef.current && mapReady) {
                    const currentHeading = currentCamera.current?.heading || lastBearing.current || 0;
                    
                    // Calculate optimal zoom based on current speed
                    const optimalZoom = getOptimalZoomLevel(speed);
                    const currentZoom = currentCamera.current?.zoom || optimalZoom;
                                                              mapRef.current.animateCamera({
                      center: {
                        latitude: newLocation.coords.latitude,
                        longitude: newLocation.coords.longitude
                      },
                      pitch: is3DView ? 45 : 0,
                      heading: currentHeading,
                      altitude: 500,
                      zoom: optimalZoom, // Use optimal zoom based on speed
                    }, { duration: 300 });
                    
                                          // Update zoom level state with optimal zoom
                      setCurrentZoomLevel(optimalZoom);
                  }
                }
              } else {
                // First location - add to path
                setPathCoordinates([newCoordinate]);
              }
              
              lastPosition.current = { 
                lat: newLocation.coords.latitude, 
                lng: newLocation.coords.longitude 
              };
              lastUpdateTime.current = currentTime;
            }
          );
          
        }

      } catch (error) {
        sensorsStarted.current = false;
      }
    };

    // Only start tracking once when map is ready
    if (mapReady && !sensorsStarted.current) {
      startLocationTracking();
    }

    return () => {
      if (locationSubscription.current) {
        locationSubscription.current.remove();
        locationSubscription.current = undefined;
      }
      
      // Clean up timers and references
      if (interactionTimeout.current) {
        clearTimeout(interactionTimeout.current);
      }
      if (regionChangeTimeout.current) {
        clearTimeout(regionChangeTimeout.current);
      }
      
      // Reset memory-intensive state
      setPathCoordinates([]);
      
      sensorsStarted.current = false;
    };
  }, [mapReady]);

  // Format elapsed time as HH:MM:SS
  const formatElapsedTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Format distance in appropriate units
  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      return `${(meters / 1000).toFixed(2)}km`;
    }
  };

  // Add maximum loading timeout
  useEffect(() => {
    const maxLoadingTimeout = setTimeout(() => {
      if (!showMap && !location) {
        setShowMap(true);
        setErrorMsg(pt.locationError);
      }
    }, 10000);

    return () => clearTimeout(maxLoadingTimeout);
  }, [showMap, location]);

  // Separate initial location fetch from continuous updates
  useEffect(() => {
    let isMounted = true;
    let locationTimeout: NodeJS.Timeout;

    const getInitialLocation = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const currentStatus = await Location.getForegroundPermissionsAsync();
        let status = currentStatus.status;
        
        if (status !== 'granted') {
          const permissionResult = await Location.requestForegroundPermissionsAsync();
          status = permissionResult.status;
        }
        
        if (status !== 'granted') {
          if (isMounted) {
            setErrorMsg(pt.permissionDenied);
            setShowMap(true);
          }
          return;
        }

        const lastKnownLocation = await Location.getLastKnownPositionAsync({
          maxAge: 300000,
        }) as Location.LocationObject | null;

        if (lastKnownLocation && isMounted) {
          setLocation(lastKnownLocation);
          setShowMap(true);
        }

        const timeoutPromise = new Promise((_, reject) => {
          locationTimeout = setTimeout(() => reject(new Error('Timeout')), 8000);
        });

        const locationPromises = [
          Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Low,
          }),
          Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Balanced,
          })
        ];

        const freshLocation = await Promise.race([
          Promise.any(locationPromises),
          timeoutPromise
        ]).catch(error => {
          if (lastKnownLocation) {
            return lastKnownLocation;
          }
          throw error;
        }) as Location.LocationObject | null;

        if (freshLocation && isMounted) {
          setLocation(freshLocation);
          setShowMap(true);
          setErrorMsg(null);
        }

      } catch (error) {
        if (isMounted) {
          setErrorMsg(pt.locationError);
          setShowMap(true);
        }
      }
    };

    getInitialLocation();

    return () => {
      isMounted = false;
      if (locationTimeout) {
        clearTimeout(locationTimeout);
      }
    };
  }, []);

  // Pulse animation for loading
  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, []);

  const retryLocation = async () => {
    setIsRetryingLocation(true);
    setErrorMsg(null);
    
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMsg(pt.permissionDenied);
        setIsRetryingLocation(false);
        return;
      }

      const newLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });
      
      setLocation(newLocation);
      setErrorMsg(null);
    } catch (error) {
      setErrorMsg(pt.locationError);
    } finally {
      setIsRetryingLocation(false);
    }
  };

  const handleMapReady = () => {
    setMapReady(true);
    
    // Initialize camera state with tilted perspective
    if (location) {
      currentCamera.current = {
        center: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude
        },
        zoom: 20,
        pitch: is3DView ? 45 : 0,
        heading: 0 // Start with north orientation
      };
      
      // Ensure the map starts with the tilted perspective
      setTimeout(() => {
        if (mapRef.current) {
          mapRef.current.animateCamera({
            center: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            },
            pitch: is3DView ? 45 : 0,
            heading: 0,
            altitude: 500,
            zoom: 20,
          }, { duration: 500 });
        }
      }, 100);
    }
    
    setTimeout(() => {
      setMapFullyLoaded(true);
    }, 1000);
  };

  // Handle user map interactions
  const handleUserInteraction = () => {
    userInteracting.current = true;
    
    // Clear existing timeout
    if (interactionTimeout.current) {
      clearTimeout(interactionTimeout.current);
    }
    
    // Resume automatic rotation after 5 seconds of no interaction
    interactionTimeout.current = setTimeout(() => {
      userInteracting.current = false;
    }, 5000);
  };

  const handleRegionChangeComplete = (region: any) => {
    // Ignore region changes caused by location updates
    if (locationUpdating.current) {
      locationUpdating.current = false;
      return;
    }
    
    // Debounce region changes to prevent excessive updates
    if (regionChangeTimeout.current) {
      clearTimeout(regionChangeTimeout.current);
    }
    
    regionChangeTimeout.current = setTimeout(() => {
      const newZoom = Math.log2(360 / region.latitudeDelta);
      
      setCurrentRegion({
        latitudeDelta: region.latitudeDelta,
        longitudeDelta: region.longitudeDelta
      });
      
      // Update current zoom level state
      setCurrentZoomLevel(newZoom);
      
      // Update current camera state to preserve user's view
      const currentHeading = region.heading !== undefined ? region.heading : (currentCamera.current?.heading || lastBearing.current || 0);
      
      currentCamera.current = {
        center: {
          latitude: region.latitude,
          longitude: region.longitude
        },
        zoom: newZoom,
        pitch: is3DView ? 45 : 0,
        heading: currentHeading
      };
      
      // Update lastBearing if we have a heading from the region
      if (region.heading !== undefined) {
        lastBearing.current = region.heading;
      }
      
      handleUserInteraction();
    }, 100);
  };

  const handleRegionChange = (region: any) => {
    // Also handle region change start (when user starts dragging/zooming)
    if (!locationUpdating.current) {
      handleUserInteraction();
      
      // Update zoom level in real-time during user interaction
      const newZoom = Math.log2(360 / region.latitudeDelta);
      setCurrentZoomLevel(newZoom);
    }
  };

  const animateToUserLocation = () => {
    if (location && mapRef.current) {
      handleUserInteraction(); // Mark as user interaction
      
      mapRef.current.animateCamera({
        center: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
        pitch: is3DView ? 45 : 0,
        heading: lastBearing.current || 0, // Use last known bearing or north
        altitude: 500,
        zoom: 17,
      }, { duration: 1000 });
    }
  };

  const toggleMapType = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      setMapType(prev => prev === 'standard' ? 'satellite' : 'standard');
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    });
  };

  const toggle3DView = async () => {
    if (location && mapRef.current) {
      try {
        // Get the actual current camera state from the map
        const actualCamera = await mapRef.current.getCamera();
        const newPitch = is3DView ? 0 : 45; // Toggle between flat (0) and tilted (45)
        
        // Use actual camera values instead of stored ones
        const currentHeading = actualCamera.heading || lastBearing.current || 0;
        const currentZoom = actualCamera.zoom || currentZoomLevel || 17;
        
        // Animate to new camera position with actual current values
        mapRef.current.animateCamera({
          center: actualCamera.center || {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          pitch: newPitch,
          heading: currentHeading,
          altitude: 500,
          zoom: currentZoom, // Use actual current zoom
        }, { duration: 500 });
        
        // Update camera state with actual values
        currentCamera.current = {
          center: actualCamera.center || {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          zoom: currentZoom,
          pitch: newPitch,
          heading: currentHeading
        };
        
        setIs3DView(!is3DView);
        handleUserInteraction(); // Mark as user interaction
      } catch (error) {
        console.error('Error getting camera state for 3D toggle:', error);
        // Fallback to original method if getCamera fails
        const newPitch = is3DView ? 0 : 45;
        mapRef.current.animateCamera({
          center: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          pitch: newPitch,
          heading: lastBearing.current || 0,
          altitude: 500,
          zoom: currentZoomLevel || 17,
        }, { duration: 500 });
        
        setIs3DView(!is3DView);
        handleUserInteraction();
      }
    }
  };

  const toggleCompassTracking = () => {
    const newState = !compassTracking;
    setCompassTracking(newState);
    handleUserInteraction(); // Mark as user interaction
    
    console.log('🧭 Compass tracking toggled:', {
      newState,
      currentHeading: location?.coords.heading,
      hasHeading: location?.coords.heading !== null
    });
    
    if (newState) {
      showAlert({
        type: 'info',
        title: 'Bússola Ativada',
        message: 'Mova-se alguns metros para calibrar a bússola. O mapa irá rodar conforme se move.',
      });
    }
  };

  const handleTerminate = () => {
    setShowTerminateModal(true);
  };

  const confirmTerminate = async () => {
    // Calculate final session data immediately
    const endTime = new Date();
    const finalElapsed = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
    
    setSessionEndTime(endTime);
    setFinalElapsedTime(finalElapsed);
    setShowTerminateModal(false);
    
    // Automatically save to database when user confirms termination
    console.log('🎯 User confirmed termination - automatically saving to database...');
    handleSendToDatabase();
  };

  const handleSendToDatabase = async () => {
    setIsSendingData(true);
    
    try {
      console.log('🎯 Starting handleSendToDatabase process...');
      
      // Mark session as completed before clearing
      const finalSessionData = {
        sessionId: monitoringSessionId,
        protocol,
        startTime: startTime.toISOString(),
        endTime: sessionEndTime?.toISOString() || new Date().toISOString(),
        totalDistance,
        contactsCount,
        pathCoordinates,
        userId: user?.uid,
        status: 'completed',
        deviceInfo: {
          platform: Platform.OS,
        }
      };
      
      console.log('📊 Final session data:', {
        sessionId: finalSessionData.sessionId,
        protocol: finalSessionData.protocol,
        duration: finalSessionData.endTime ? 
          Math.floor((new Date(finalSessionData.endTime).getTime() - new Date(finalSessionData.startTime).getTime()) / 1000) : 
          'unknown',
        totalDistance: finalSessionData.totalDistance,
        contactsCount: finalSessionData.contactsCount,
        pathPoints: finalSessionData.pathCoordinates.length,
        userId: finalSessionData.userId,
        status: finalSessionData.status
      });
      
      // Save final session data for sync
      await saveOfflineMonitoringSession(finalSessionData);
      console.log('✅ Session data saved to offline storage');
      
      // Check what's stored in offline storage
      const allSessions = await AsyncStorage.getItem('offlineMonitoringSessions');
      const allContacts = await AsyncStorage.getItem('offlineContactEvents');
      const allGPS = await AsyncStorage.getItem('offlineGPSPoints');
      
      console.log('📱 Offline storage summary:', {
        sessions: allSessions ? JSON.parse(allSessions).length : 0,
        contacts: allContacts ? JSON.parse(allContacts).length : 0,
        gpsPoints: allGPS ? JSON.parse(allGPS).length : 0
      });
      
      // Get report name from stored session data
      let reportName = null;
      try {
        const sessionData = await AsyncStorage.getItem('activeMonitoringSession');
        if (sessionData) {
          const session = JSON.parse(sessionData);
          reportName = session.reportName || null;
        }
      } catch (error) {
        console.error('Error loading session data for report name:', error);
      }

      // Create a proper report for the database (whether online or offline)
      const reportData = {
        sessionId: monitoringSessionId,
        userId: user?.uid,
        userName: user?.displayName || user?.email || 'Utilizador',
        userEmail: user?.email || '',
        userRole: 'tecnico_prorola',
        type: 'tecnico_monitoring_report',
        reportName: reportName,
        protocol,
        startTime: startTime.toISOString(),
        endTime: sessionEndTime?.toISOString() || new Date().toISOString(),
        sessionDuration: finalElapsedTime,
        totalDistance,
        contactsCount,
        pathCoordinates,
        location: location ? {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        } : null,
        weather: weatherData || null,
        deviceInfo: {
          platform: Platform.OS,
        },
        createdAt: new Date().toISOString(),
        status: 'completed',
        observersCount: observersCount || 1,
        source: 'mobile', // Mark as GPS/mobile created
      };
      
      // Check internet connectivity and save accordingly
      const netInfo = await NetInfo.fetch();
      console.log('🌐 Network status:', {
        isConnected: netInfo.isConnected,
        type: netInfo.type,
        isInternetReachable: netInfo.isInternetReachable
      });
      
      // Check if this report has already been saved to avoid duplicates
      const existingSyncId = `monitoring_${user?.uid}_${monitoringSessionId}`;
      const pendingReports = await AsyncStorage.getItem('pendingReports');
      const existingReports = pendingReports ? JSON.parse(pendingReports) : [];
      const alreadySaved = existingReports.some((report: any) => 
        report.sessionId === monitoringSessionId || 
        report.syncId === existingSyncId
      );
      
      if (alreadySaved) {
        console.log('⚠️ Report already saved, skipping to avoid duplicates');
        setShowSummaryModal(false);
        onTerminate();
        return;
      }
      
      // IMPROVED: Always save offline first for reliability, then sync in background
      console.log('💾 Saving data offline first for reliability...');
      
      // Store as pending report for sync (works with or without internet)
      const pendingReport = {
        syncId: existingSyncId,
        ...reportData,
        type: 'tecnico_monitoring_report',
        localImageUris: [],
        images: [],
      };
      
      existingReports.push(pendingReport);
      await AsyncStorage.setItem('pendingReports', JSON.stringify(existingReports));
      console.log('✅ Monitoring report saved offline successfully');
      
      // Now try to sync in background if internet is available
      if (netInfo.isConnected) {
        console.log('🌐 Internet available, starting background sync...');
        
        try {
          // Check if there are images to sync and show progress if needed
          const hasImagesToSync = await monitoringSyncService.hasImagesToSync();
          console.log('🔄 Starting background sync...', hasImagesToSync ? 'with images' : 'no images');
          
          if (hasImagesToSync) {
            // Show progress modal for image uploads
            setIsUploadingContactImages(true);
            setContactUploadProgress(0);
            setUploadMessage('Preparando imagens...');
          }
          
          // Try direct report save to Firestore
          const db = getFirestore();
          await addDoc(collection(db, 'reports'), {
            ...reportData,
            createdAt: serverTimestamp(),
          });
          console.log('✅ Report also saved directly to database');
          
          // Sync additional offline data
          await monitoringSyncService.syncOfflineData();
          console.log('✅ Background sync completed successfully');
          
          // Hide progress modal if it was shown
          if (hasImagesToSync) {
            setIsUploadingContactImages(false);
            setContactUploadProgress(0);
            setUploadMessage('');
          }
          
        } catch (syncError) {
          console.error('⚠️ Background sync failed (data is safe offline):', syncError);
          
          // Hide progress modal if it was shown
          setIsUploadingContactImages(false);
          setContactUploadProgress(0);
          setUploadMessage('');
          
          // Data is already saved offline, so this is not critical
          console.log('📱 Data is safely stored offline and will sync when connection improves');
        }
      } else {
        console.log('📡 No internet - data saved offline and will sync when connection is available');
      }
      
      // Clear active monitoring data after successful save
      await AsyncStorage.removeItem('monitoringData');
      console.log('🗑️ Active monitoring data cleared');
      
      // Clear current monitoring session for contact-details
      await AsyncStorage.removeItem('currentMonitoringSession');
      console.log('🗑️ Current monitoring session cleared');
      
      setShowSummaryModal(false);
      
      // Show a brief loading state before terminating
      console.log('🔄 Finalizing monitoring session...');
      
      // Small delay to ensure all sync operations complete
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Terminate the monitoring session and let parent handle everything
      onTerminate();
      
    } catch (error) {
      console.error('❌ Error saving monitoring data:', error);
      showAlert({
        type: 'error',
        message: 'Erro ao guardar os dados da sessão. Tente novamente.'
      });
    } finally {
      setIsSendingData(false);
    }
  };

  const cancelTerminate = () => {
    setShowTerminateModal(false);
  };

  const cancelSendToDatabase = () => {
    setShowSummaryModal(false);
  };

  // Handle Android back button to prevent accidental exit
  useFocusEffect(
    useCallback(() => {
      // Reload contact markers when screen comes back into focus
      loadContactMarkers();
      
      const onBackPress = () => {
        // Show terminate confirmation instead of allowing back navigation
        setShowTerminateModal(true);
        return true; // Prevent default back action
      };

      if (Platform.OS === 'android') {
        const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
        return () => subscription.remove();
      }
    }, [])
  );

  // Contact modal handlers
  const handleContactButtonPress = async () => {
    if (location) {
      // Get current camera state directly from map
      try {
        if (mapRef.current) {
          const currentCameraState = await mapRef.current.getCamera();
          console.log('Current camera state from map:', currentCameraState);
          
          // Update our camera reference with actual current state
          currentCamera.current = {
            center: currentCameraState.center,
            zoom: currentCameraState.zoom || 17,
            pitch: currentCameraState.pitch || 45,
            heading: currentCameraState.heading || 0
          };
          
          // Update states with actual values
          setCurrentZoomLevel(currentCameraState.zoom || 17);
          lastBearing.current = currentCameraState.heading || 0;
        }
      } catch (error) {
        console.log('Error getting camera state:', error);
      }
      
      // Debug: log current zoom level when opening modal
      console.log('Opening contact modal with currentZoomLevel:', currentZoomLevel);
      console.log('Camera zoom:', currentCamera.current?.zoom);
      console.log('Camera heading/bearing:', currentCamera.current?.heading);
      console.log('lastBearing.current:', lastBearing.current);
      setShowContactModal(true);
    }
  };

  const handleContactModalClose = () => {
    setShowContactModal(false);
    setCurrentContactData(null);
  };

  // Handle location placement confirmation - move to details step
  const handleLocationPlacementConfirm = async (observerLocation: {latitude: number, longitude: number}, contactLocation: {latitude: number, longitude: number}, distance: number) => {
    // Calculate bearing from observer to contact location
    const bearing = calculateBearing(
      observerLocation.latitude,
      observerLocation.longitude,
      contactLocation.latitude,
      contactLocation.longitude
    );
    
    // Store the contact data
    setCurrentContactData({
      observerLocation,
      contactLocation,
      distance,
      bearing
    });
    
    // Close location modal and navigate to details screen
    setShowContactModal(false);
    router.push({
      pathname: '/contact-details',
      params: {
        distance: distance.toString(),
        bearing: bearing.toString(),
        observerLat: observerLocation.latitude.toString(),
        observerLng: observerLocation.longitude.toString(),
        contactLat: contactLocation.latitude.toString(),
        contactLng: contactLocation.longitude.toString(),
      }
    });
  };



  return (
    <SafeAreaView style={styles.container}>
      <SafeSystemBars 
        style="light" 
        translucent={true} 
        backgroundColor="#0996a8"
        navigationBarColor="#0996a8"
      />
      
      {/* Simple header - NO edge-to-edge, NO complex layout */}
      <View style={{
        backgroundColor: '#0996a8',
        paddingVertical: 6,
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        <Text style={{
          fontSize: 16,
          color: '#ffffff',
          fontWeight: '600',
        }}>{pt.monitoringActive}</Text>
        <Text style={{
          fontSize: 12,
          color: '#ffffff',
          opacity: 0.8,
          marginTop: 2,
        }}>
          {pt.protocolOptions[protocol]}
        </Text>
      </View>

      {/* NetworkStatusIndicator positioned over the map */}
      {/* Commented out during monitoring to avoid distraction
      <View style={{
        position: 'absolute',
        top: 115,
        left: 0,
        right: 0,
        alignItems: 'center',
        zIndex: 1000,
      }}>
        <NetworkStatusIndicator style={{
          transform: [{ scale: 0.8 }],
        }} />
      </View>
      */}

      {/* Header Logo - positioned on top of everything */}
      <View style={{
        position: 'absolute',
        left: 12,
        top: isLandscape ? 35 :30,
        zIndex: 99999,
      }}>
        <Image 
          source={require('../assets/images/header-logo.png')}
          style={{
            width: isLandscape ? 60 : 90,
            height: isLandscape ? 60 : 90,
            resizeMode: 'contain',
          }}
        />
      </View>

      {/* Map Container */}
      <View style={styles.mapContainer}>
        {Platform.OS === 'web' ? (
          <View style={[styles.noMapContainer, styles.webContainer]}>
            <Text style={styles.noMapText}>O mapa não está disponível na versão web.</Text>
          </View>
        ) : (
          <>
            {showMap && location ? (
              <>
                <Animated.View style={[styles.map, { opacity: fadeAnim }]}>
                  <MapView
                    ref={mapRef}
                    style={styles.map}
                    provider={PROVIDER_GOOGLE}
                    mapType={mapType}
                    initialCamera={{
                      center: {
                        latitude: location.coords.latitude,
                        longitude: location.coords.longitude,
                      },
                      pitch: is3DView ? 45 : 0, // Start tilted for 3D navigation view or flat for 2D
                      heading: 0, // Start facing north
                      altitude: 500,
                      zoom: 20,
                    }}
                    showsUserLocation={true}
                    showsMyLocationButton={false}
                    onUserLocationChange={(event) => {
                      const mapAccuracy = event.nativeEvent.coordinate.accuracy;
                      
                      // Use MapView's accuracy if it's significantly different from our tracked accuracy
                      if (mapAccuracy && Math.abs(mapAccuracy - (gpsAccuracy || 0)) > 1) {
                        console.log(`🗺️ Using MapView accuracy: ${mapAccuracy.toFixed(1)}m (was ${gpsAccuracy ? gpsAccuracy.toFixed(1) : 'null'}m)`);
                        setGpsAccuracy(mapAccuracy);
                        setGpsAccuracyStatus(classifyGpsAccuracy(mapAccuracy));
                        setRenderKey(k => k + 1);
                      }
                    }}

                    followsUserLocation={false}
                    showsCompass={false}
                    rotateEnabled={true}
                    pitchEnabled={true}
                    scrollEnabled={true}
                    zoomEnabled={true}
                    onMapReady={handleMapReady}
                    onRegionChange={handleRegionChange}
                    onRegionChangeComplete={handleRegionChangeComplete}
                    onPress={handleRegionChange}
                    loadingEnabled={true}
                    loadingIndicatorColor="#0996a8"
                    loadingBackgroundColor="#ffffff"
                    customMapStyle={[
                      {
                        featureType: "poi",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.business",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.medical",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.park",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.place_of_worship",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.school",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.sports_complex",
                        stylers: [{ visibility: "off" }]
                      }
                    ]}
                  >
                    {/* Removed marker since we have user location dot */}
                    
                    {/* Sighting lines and contact markers */}
                    {contactMarkers.map((marker) => (
                      <React.Fragment key={`sighting-${marker.id}`}>
                        {/* Dashed sighting line from observer to contact */}
                        <Polyline
                          coordinates={[marker.observerLocation, marker.coordinate]}
                          strokeColor="#10B981"
                          strokeWidth={2}
                          lineDashPattern={[10, 5]}
                        />
                        
                        {/* Observer position marker (small blue dot) */}
                        <Marker
                          coordinate={marker.observerLocation}
                          anchor={{ x: 0.5, y: 0.5 }}
                          title={`Observador ${marker.contactNumber}`}
                        >
                          <View style={styles.observerMarker}>
                            <Text style={styles.observerNumber}>{marker.contactNumber}</Text>
                          </View>
                        </Marker>
                        
                        {/* Contact position marker (green dove) */}
                        <Marker
                          coordinate={marker.coordinate}
                          anchor={{ x: 0.5, y: 0.5 }}
                          title={`Contacto ${marker.contactNumber}`}
                          description={new Date(marker.timestamp).toLocaleTimeString('pt-PT', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        >
                          <View style={styles.contactMarker}>
                            <DoveIcon size={20} color="#FFFFFF" />
                          </View>
                        </Marker>
                      </React.Fragment>
                    ))}
                    
                    {/* Path traveled - show as polyline with start/end markers when no preloaded trajectory */}
                    {pathCoordinates.length > 1 && (
                      <>
                        <Polyline
                          coordinates={pathCoordinates}
                          strokeColor="#0996a8"
                          strokeWidth={4}
                        />
                        
                        {/* Only show start marker during active monitoring, end marker only when finished */}
                        {!selectedTrajectory && (
                          <>
                            {/* Start marker for GPS path - always show */}
                            <Marker
                              coordinate={pathCoordinates[0]}
                              anchor={{ x: 0.5, y: 0.5 }}
                              title="Início da Monitorização"
                              description="Local onde começou a monitorização"
                            >
                              <View style={styles.trajectoryStartMarker}>
                                <FontAwesome name="play" size={12} color="#FFFFFF" />
                              </View>
                            </Marker>
                            
                            {/* End marker only shows in summary/completed trajectories, not during active monitoring */}
                          </>
                        )}
                      </>
                    )}
                    

                    
                    {/* Loaded trajectory - show as blue polyline with start/end markers */}
                    {trajectoryCoordinates.length > 1 && (
                      <>
                        <Polyline
                          coordinates={trajectoryCoordinates}
                          strokeColor="#3B82F6"
                          strokeWidth={5}
                          zIndex={1000}
                        />
                        
                        {/* Start marker (green) */}
                        <Marker
                          coordinate={trajectoryCoordinates[0]}
                          anchor={{ x: 0.5, y: 0.5 }}
                          title="Início do Trajeto"
                          description={selectedTrajectory?.name || 'Trajeto carregado'}
                        >
                          <View style={styles.trajectoryStartMarker}>
                            <FontAwesome name="play" size={12} color="#FFFFFF" />
                          </View>
                        </Marker>
                        
                        {/* End marker (red) */}
                        <Marker
                          coordinate={trajectoryCoordinates[trajectoryCoordinates.length - 1]}
                          anchor={{ x: 0.5, y: 0.5 }}
                          title="Fim do Trajeto"
                          description={selectedTrajectory?.name || 'Trajeto carregado'}
                        >
                          <View style={styles.trajectoryEndMarker}>
                            <FontAwesome name="stop" size={12} color="#FFFFFF" />
                          </View>
                        </Marker>
                      </>
                    )}
                  </MapView>
                </Animated.View>



                {/* Map Controls */}
                <View style={styles.mapControlsContainer}>
                  <TouchableOpacity
                    style={styles.mapControlButton}
                    onPress={animateToUserLocation}
                  >
                    <FontAwesome name="crosshairs" size={20} color="#0996a8" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.mapControlButton, { marginTop: 8 }]}
                    onPress={toggleMapType}
                  >
                    <FontAwesome 
                      name={mapType === 'standard' ? 'globe' : 'map'} 
                      size={20} 
                      color="#0996a8" 
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.mapControlButton, { marginTop: 8 }]}
                    onPress={toggle3DView}
                  >
                    <FontAwesome 
                      name={is3DView ? 'cube' : 'square-o'} 
                      size={20} 
                      color="#0996a8" 
                    />
                  </TouchableOpacity>

                </View>

                {/* GPS Accuracy Indicator - Only show when GPS is fair or poor */}
                {(gpsAccuracyStatus === 'fair' || gpsAccuracyStatus === 'poor') && (
                  <View style={styles.gpsAccuracyContainer}>
                    <View style={[styles.gpsAccuracyIndicator, { backgroundColor: getGpsAccuracyColor(gpsAccuracyStatus) }]}>
                      <FontAwesome name="location-arrow" size={12} color="#fff" />
                      <Text style={styles.gpsAccuracyText} key={`top-accuracy-${renderKey}`}>
                        {gpsAccuracy ? `${Math.round(gpsAccuracy)}m` : '--'}
                      </Text>
                    </View>
                    <Text style={styles.gpsAccuracyLabel}>
                      {getGpsAccuracyText(gpsAccuracyStatus)}
                    </Text>
                  </View>
                )}





                {/* Location Status */}
                {errorMsg && (
                  <View style={styles.locationStatusContainer}>
                    <View style={styles.locationStatusCard}>
                      <FontAwesome name="exclamation-triangle" size={16} color="#ff9500" />
                      <Text style={styles.locationStatusText}>{pt.gpsUnavailable}</Text>
                      <TouchableOpacity onPress={retryLocation} disabled={isRetryingLocation}>
                        {isRetryingLocation ? (
                          <ActivityIndicator size="small" color="#0996a8" />
                        ) : (
                          <FontAwesome name="refresh" size={14} color="#0996a8" />
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </>
            ) : (
              <View style={styles.noMapContainer}>
                <View style={styles.loadingCard}>
                  <View style={styles.logoContainer}>
                    <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                      <FontAwesome name="map-marker" size={35} color="#0996a8" />
                    </Animated.View>
                  </View>
                  <Text style={styles.noMapText}>A carregar mapa...</Text>
                  <View style={styles.loadingIndicatorContainer}>
                    <ActivityIndicator size="small" color="#0996a8" />
                  </View>
                </View>
              </View>
            )}

            {!mapFullyLoaded && showMap && (
              <View style={styles.mapLoadingContainer}>
                <View style={styles.loadingCard}>
                  <View style={styles.logoContainer}>
                    <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                      <FontAwesome name="map-marker" size={35} color="#0996a8" />
                    </Animated.View>
                  </View>
                  <Text style={styles.noMapText}>A carregar mapa...</Text>
                  <View style={styles.loadingIndicatorContainer}>
                    <ActivityIndicator size="small" color="#0996a8" />
                  </View>
                </View>
              </View>
            )}
          </>
        )}

        {/* Bottom Controls */}
        <View style={[styles.bottomControls, { paddingBottom: 10 }]}>
          <View style={styles.statsContainer}>
            <View style={styles.statRow}>
              <FontAwesome name="clock-o" size={12} color="#fff" style={styles.statIcon} />
              <Text style={styles.statLabel}>{pt.timeElapsed}</Text>
              <Text style={styles.statValue}>{formatElapsedTime(elapsedTime)}</Text>
            </View>
            
            <View style={styles.statRow}>
              <FontAwesome name="male" size={12} color="#fff" style={styles.statIcon} />
              <Text style={styles.statLabel}>{pt.distanceTraveled}</Text>
              <Text style={styles.statValue}>{formatDistance(totalDistance)}</Text>
            </View>

            <View style={styles.statRow}>
              <FontAwesome name="tachometer" size={12} color="#fff" style={styles.statIcon} />
              <Text style={styles.statLabel}>Velocidade:</Text>
              <Text style={styles.statValue}>{(currentSpeed * 3.6).toFixed(1)} km/h</Text>
            </View>

            <View style={styles.statRow}>
              <DoveIcon size={12} color="#fff" style={styles.statIcon} />
              <Text style={styles.statLabel}>{pt.contactsCount}</Text>
              <Text style={styles.statValue}>{contactsCount}</Text>
            </View>

            <View style={styles.statRow} key={`accuracy-${renderKey}`}>
              <FontAwesome name="location-arrow" size={12} color="#fff" style={styles.statIcon} />
              <Text style={styles.statLabel}>{pt.gpsAccuracy}</Text>
              <Text style={styles.statValue} key={`accuracy-value-${renderKey}`}>
                {gpsAccuracy ? `±${Math.round(gpsAccuracy)}m` : '--'}
              </Text>

            </View>

          </View>
          
          <TouchableOpacity
            style={styles.terminateButton}
            onPress={handleTerminate}
            activeOpacity={0.7}
          >
            <FontAwesome name="stop" size={20} color="#fff" style={styles.terminateIcon} />
            <Text style={styles.terminateText}>{pt.terminate}</Text>
          </TouchableOpacity>
        </View>



        {/* Floating Report Button */}
        <View style={[styles.buttonContainer, { bottom: insets.bottom + 70 }]}>
          <TouchableOpacity
            style={[styles.button, styles.reportButton]}
            onPress={handleContactButtonPress}
            activeOpacity={0.8}
          >
            <DoveIcon size={20} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.buttonText}>Contacto</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Terminate Confirmation Modal */}
      <Modal
        visible={showTerminateModal}
        transparent={true}
        animationType="fade"
        onRequestClose={cancelTerminate}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <FontAwesome name="question-circle" size={48} color="#0996a8" />
              <Text style={styles.modalTitle}>{pt.confirmTerminate}</Text>
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={cancelTerminate}
                activeOpacity={0.7}
              >
                <FontAwesome name="times" size={16} color="#fff" style={styles.buttonIcon} />
                <Text style={styles.cancelButtonText}>{pt.no}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={confirmTerminate}
                activeOpacity={0.7}
              >
                <FontAwesome name="check" size={16} color="#fff" style={styles.buttonIcon} />
                <Text style={styles.confirmButtonText}>{pt.yes}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Session Summary Modal */}
      <Modal
        visible={showSummaryModal}
        transparent={true}
        animationType="fade"
        onRequestClose={cancelSendToDatabase}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.summaryModalContainer}>
            <View style={styles.modalHeader}>
              <FontAwesome name="file-text-o" size={48} color="#0996a8" />
              <Text style={styles.modalTitle}>Resumo da Monitoração</Text>
            </View>
            
            <View style={styles.summaryContent}>
              {/* Protocol section - custom layout */}
              <View style={[styles.summaryRow, { flexDirection: 'column', alignItems: 'center', marginBottom: 20 }]}>
                <Text style={[styles.summaryLabel, { marginBottom: 2, fontWeight: 'bold', fontSize: 16, textAlign: 'center' }]}>Protocolo</Text>
                <Text style={[styles.summaryValue, { textAlign: 'center' }]}>{pt.protocolOptions[protocol]}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <FontAwesome name="clock-o" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Duração:</Text>
                <Text style={styles.summaryValue}>{formatElapsedTime(finalElapsedTime)}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <FontAwesome name="male" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Distância:</Text>
                <Text style={styles.summaryValue}>{formatDistance(totalDistance)}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <DoveIcon size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Contactos:</Text>
                <Text style={styles.summaryValue}>{contactsCount}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <FontAwesome name="users" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Observadores:</Text>
                <Text style={styles.summaryValue}>{observersCount || 1}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <FontAwesome name="calendar" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Início:</Text>
                <Text style={styles.summaryValue}>{startTime.toLocaleTimeString('pt-PT', { hour: '2-digit', minute: '2-digit' })}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <FontAwesome name="calendar" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Fim:</Text>
                <Text style={styles.summaryValue}>{sessionEndTime?.toLocaleTimeString('pt-PT', { hour: '2-digit', minute: '2-digit' }) || '--:--'}</Text>
              </View>
              
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton, { alignSelf: 'center', minWidth: 120 }]}
                onPress={handleSendToDatabase}
                activeOpacity={0.7}
                disabled={isSendingData}
              >
                {isSendingData ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <>
                    <FontAwesome name="check" size={16} color="#fff" style={styles.buttonIcon} />
                    <Text style={styles.confirmButtonText}>OK</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      <CustomAlert
        visible={isVisible}
        type={config.type}
        title={config.title}
        message={config.message}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />

      <ContactPlacementModal
        visible={showContactModal}
        onClose={handleContactModalClose}
        onConfirm={handleLocationPlacementConfirm}
        userLocation={location}
        currentBearing={currentCamera.current?.heading || lastBearing.current || 0}
        currentZoom={currentZoomLevel}
        currentMapType={mapType}
      />

      {/* Upload Progress Modal for Contact Images - Only shown when actually uploading after summary */}
      <Modal
        visible={isUploadingContactImages}
        transparent={true}
        animationType="fade"
        onRequestClose={() => {}}
      >
        <View style={styles.progressModalContainer}>
          <View style={styles.progressModalContent}>
            <View style={styles.progressModalHeader}>
              <FontAwesome name="cloud-upload" size={32} color="#0996a8" />
              <Text style={styles.progressModalTitle}>
                {uploadMessage || 'A carregar fotos...'}
              </Text>
            </View>
            
            <View style={styles.progressBarContainer}>
              <View style={[styles.progressBar, { width: `${contactUploadProgress}%` }]} />
            </View>
            
            <Text style={styles.progressText}>
              {Math.round(contactUploadProgress)}%
            </Text>
            
            <ActivityIndicator 
              size="large" 
              color="#0996a8" 
              style={styles.progressSpinner}
            />
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0996a8',
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
    marginTop: 0,
  },
  mapLoadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    zIndex: 1000,
    elevation: 1000,
  },
  noMapContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0996a8',
  },
  loadingCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    width: '85%',
    maxWidth: 340,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  logoContainer: {
    marginBottom: 16,
  },
  noMapText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
    textAlign: 'center',
  },
  loadingIndicatorContainer: {
    marginTop: 16,
  },
  webContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapControlsContainer: {
    position: 'absolute',
    right: 10,
    top: 20,
    backgroundColor: 'transparent',
    alignItems: 'center',
    zIndex: 999,
  },
  mapControlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    width: 40,
    height: 40,
    borderRadius: 2,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 1,
    shadowRadius: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  locationStatusContainer: {
    position: 'absolute',
    top: 20,
    left: 10,
    right: 10,
    zIndex: 999,
  },
  locationStatusCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderLeftWidth: 4,
    borderLeftColor: '#ff9500',
  },
  locationStatusText: {
    color: '#ff9500',
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    marginLeft: 8,
  },
  bottomControls: {
    backgroundColor: '#0996a8',
    paddingTop: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  statsContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    flex: 1,
    marginRight: 20,
  },
  statRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 0,
  },
  statIcon: {
    marginRight: 8,
    color: '#fff',
    width: 14,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: 9,
    color: 'rgba(255, 255, 255, 0.9)',
    marginRight: 0,
    minWidth: 45,
    fontWeight: '400',
  },
  statValue: {
    fontSize: 11,
    fontWeight: '500',
    color: '#fff',
  },
  terminateButton: {
    backgroundColor: '#dc3545',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  terminateIcon: {
    marginRight: 8,
  },
  terminateText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    width: '85%',
    maxWidth: 340,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 12,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#6B7280',
  },
  confirmButton: {
    backgroundColor: '#0996a8',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    alignItems: 'center',
    gap: 10,
  },
  button: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  reportButton: {
    backgroundColor: '#0996a8',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  summaryModalContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    width: '90%',
    maxWidth: 380,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryContent: {
    marginBottom: 24,
  },
  summaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 8,
  },
  summaryIcon: {
    marginRight: 12,
    width: 20,
    textAlign: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    flex: 1,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#0996a8',
    textAlign: 'right',
  },
  progressModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressModalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    width: '85%',
    maxWidth: 340,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  progressModalHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  progressModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 12,
    textAlign: 'center',
  },
  progressBarContainer: {
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
    height: 20,
    marginBottom: 12,
  },
  progressBar: {
    backgroundColor: '#0996a8',
    borderRadius: 8,
    height: '100%',
  },
  progressText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  progressSpinner: {
    marginTop: 16,
  },
  observerMarker: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#3B82F6', // Blue color similar to webadmin
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  observerNumber: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  contactMarker: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#10B981', // Green color similar to webadmin
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  gpsAccuracyContainer: {
    position: 'absolute',
    top: 20,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 998,
  },
  gpsAccuracyIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
  },
  gpsAccuracyText: {
    color: '#fff',
    fontSize: 11,
    fontWeight: '600',
    marginLeft: 4,
  },
  gpsAccuracyLabel: {
    color: '#333',
    fontSize: 10,
    fontWeight: '500',
    marginTop: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    textAlign: 'center',
    minWidth: 60,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },

  trajectoryStartMarker: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#10B981', // Green color for start
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  trajectoryEndMarker: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#EF4444', // Red color for end
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },


}); 