import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  pt: {
    translation: {
      // Common
      success: 'Êxito',
      error: 'Erro',
      yes: 'Sim',
      no: 'N<PERSON>',
      ok: 'OK',

      // Profile
      profileUpdated: 'Perfil atualizado com êxito.',
      updateProfileError: 'Erro ao atualizar o perfil.',
      passwordResetSent: 'Email de redefinição de palavra-passe enviado.',
      deleteAccountTitle: 'Eliminar Conta',
      deleteAccountConfirm: 'Tem a certeza que pretende eliminar a sua conta?',
      accountDeleted: 'Conta eliminada com êxito.',
      deleteAccountError: 'Erro ao eliminar a conta.',
      errorUploadingPhoto: 'Erro ao carregar a fotografia.',

      // Reports
      deleteReport: 'Eliminar Relatório',
      confirmDeleteReport: 'Tem a certeza que pretende eliminar este relatório?',
      reportDeleted: 'Relatório eliminado com êxito.',
      deleteReportError: 'Erro ao eliminar o relatório.',
      deleteImages: 'Eliminar Fotografias',
      confirmDeleteImages: 'Tem a certeza que pretende eliminar as fotografias selecionadas?',
      imagesDeleted: 'Fotografias eliminadas com êxito.',
      deleteImagesError: 'Erro ao eliminar as fotografias.',
      noImagesSelected: 'Nenhuma fotografia selecionada.',
      selectedCount: 'Selecionadas: {{count}}',
      deleteSelected: 'Eliminar',
      cancelSelection: 'Cancelar',

      // Alert Buttons
      cancel: 'Cancelar',
      delete: 'Eliminar',
      confirm: 'Confirmar',
      save: 'Guardar',
      close: 'Fechar',
    },
  },
};

// Initialize i18next
i18n
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v4',
    resources,
    lng: 'pt', // default language
    fallbackLng: 'pt',
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false, // prevents suspense issues in React Native
    },
  });

export default i18n; 