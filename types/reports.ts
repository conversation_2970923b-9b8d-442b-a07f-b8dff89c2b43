// Report interfaces for different user roles

export interface ReportCircumstances {
  rolaAdultaCantando: boolean;
  rolaEmVoo: boolean;
  adultoPousado: boolean;
  adultoEmDisplay: boolean;
  ninhoVazio: boolean;
  nichoOcupado: boolean;
  ovos: boolean;
  adultoAIncubar: boolean;
  crias: boolean;
  juvenile: boolean;
  outraQual: boolean;
  outraQualText: string;
}

export interface ReportContactLocation {
  arvore: boolean;
  arbusto: boolean;
  pontoDeAgua: boolean;
  clareira: boolean;
  parcelaAgricola: boolean;
  outraQual: boolean;
  outraQualText: string;
}

// New interfaces for tecnico reports with protocol-based structure
export type TecnicoProtocol = 
  | 'trajeto'
  | 'estacoes_escuta'
  | 'metodo_mapas'
  | 'contagens_pontos'
  | 'captura_marcacao'
  | 'acompanhamento_cacadas'
  | 'registos_ocasionais';

export interface TecnicoReportCircumstances {
  adultoMacho: boolean;
  adultoFemea: boolean;
  adultoSexoIndeterminado: boolean;
  juvenilMacho: boolean;
  juvenilFemea: boolean;
  juvenilSexoIndeterminado: boolean;
  ninhoComOvos: boolean;
  ninhoComCrias: boolean;
  ninhoAbandonado: boolean;
  comportamentoTerritorial: boolean;
  comportamentoAlimentar: boolean;
  comportamentoReprodutor: boolean;
  migracaoAtiva: boolean;
  outraQual: boolean;
  outraQualText: string;
}

export interface TecnicoReportHabitat {
  floresta: boolean;
  matos: boolean;
  olival: boolean;
  vinha: boolean;
  culturasCereais: boolean;
  pastagem: boolean;
  zonaRibeirinha: boolean;
  zonaUrbana: boolean;
  zonaRural: boolean;
  outraQual: boolean;
  outraQualText: string;
}

export interface TecnicoReportMetrics {
  numeroIndividuos: number;
  distanciaObservacao: number;
  duracaoObservacao: number;
  condicoesMeteorolgicas: string;
  visibilidade: string;
  perturbacoes: string;
  // Protocol-specific fields
  extensaoTrajeto?: number; // for trajeto
  numeroEstacoes?: number; // for estacoes_escuta
  tempoEscutaPorEstacao?: number; // for estacoes_escuta
  areaMapeada?: number; // for metodo_mapas
  numeroIndividuosMarcados?: number; // for captura_marcacao
  tipoMarcacao?: string; // for captura_marcacao
  numeroCacadores?: number; // for acompanhamento_cacadas
  tipoCacada?: string; // for acompanhamento_cacadas
}

export interface WeatherConditions {
  // Required fields - always present
  temperature: number;
  feelsLike: number;
  description: string;
  humidity: number;
  pressure: number;
  windSpeed: number;
  windDirection: number;
  visibility: number;
  cloudiness: number;
  icon: string;
  timestamp: number;
  sunrise: number;
  sunset: number;
  moonPhase: number;
  barometricTrend: string;
  
  // Optional fields - only included if available from weather API
  pressureTrend?: 'rising' | 'falling' | 'steady';
  windGust?: number;
  uvIndex?: number;
  dewPoint?: number;
}

export interface PendingReport {
  syncId?: string; // Unique ID to prevent duplicate syncing
  userId: string;
  userName: string;
  userEmail: string;
  type: 'colaborador_report' | 'tecnico_report' | 'gestor_monitoring_report' | 'gestor_report';
  location: {
    latitude: number;
    longitude: number;
  };
  comment: string;
  images?: string[];
  localImageUris?: string[];
  createdAt: string;
  deviceInfo?: {
    platform: string;
  };
  // Fields for colaborador reports
  circumstances?: ReportCircumstances;
  contactLocation?: ReportContactLocation;
  // Fields for tecnico reports
  reportName?: string;
  tecnicoProtocol?: TecnicoProtocol;
  tecnicoCircumstances?: TecnicoReportCircumstances;
  tecnicoHabitat?: TecnicoReportHabitat;
  tecnicoMetrics?: TecnicoReportMetrics;
  weather?: WeatherConditions;
  // Fields for gestor mobile trajectories
  isRedoOperation?: boolean;
  trajectoryToRedoId?: string | null;
  // Additional fields for gestor monitoring reports
  sessionId?: string;
  userRole?: string;
  zoneId?: string;
  zoneName?: string;
  protocol?: string;
  startTime?: string;
  endTime?: string;
  sessionDuration?: number;
  totalDistance?: number;
  contactsCount?: number;
  pathCoordinates?: Array<{latitude: number, longitude: number}>;
  observersCount?: number;
}

export type ReportType = 'colaborador_report' | 'tecnico_report';

export interface ColaboradorReportData {
  circumstances: ReportCircumstances;
  contactLocation: ReportContactLocation;
  images: string[];
  weather?: WeatherConditions;
}

export interface TecnicoReportData {
  reportName: string;
  protocol: TecnicoProtocol;
  habitat: TecnicoReportHabitat;
  metrics: TecnicoReportMetrics;
  images: string[];
  weather?: WeatherConditions;
}

// Gestor report types - similar to tecnico but for hunt managers
export interface GestorReport {
  id: string;
  userId: string;
  userName: string;
  userEmail?: string;
  userRole: 'gestor_caca';
  type: 'gestor_monitoring_report' | 'gestor_report';
  // Core gestor fields
  protocol: string; // Usually 'trajeto' for gestores
  sessionId?: string; // For monitoring sessions
  comment?: string;
  createdAt: any; // Timestamp
  images?: string[];
  location: {
    latitude: number;
    longitude: number;
  };
  // Zone management fields
  zoneId: string;
  zoneName: string;
  // Device information
  deviceInfo: {
    platform: string;
    model?: string;
    os?: string;
    version?: string;
  };
  // Weather conditions
  weather?: {
    temperature: number;
    feelsLike: number;
    description: string;
    humidity: number;
    pressure: number;
    pressureTrend?: 'rising' | 'falling' | 'steady';
    windSpeed: number;
    windDirection: number;
    windGust?: number;
    visibility: number;
    cloudiness: number;
    uvIndex?: number;
    dewPoint?: number;
    icon: string;
    timestamp: number;
    sunrise: number;
    sunset: number;
    moonPhase?: number;
    barometricTrend?: string;
  };
  // Session management fields (for monitoring reports)
  observersCount?: number;
  startTime?: any; // Timestamp
  endTime?: any; // Timestamp
  sessionDuration?: number; // in seconds
  totalDistance?: number; // in meters
  // Contact tracking
  contactEventsCount?: number;
  pathCoordinates?: Array<{latitude: number, longitude: number}>;
  // Gestor-specific fields
  managementActivity?: {
    activityType: 'patrulha' | 'manutencao' | 'observacao' | 'gestao_habitat' | 'outro';
    description?: string;
    equipmentUsed?: string[];
    areasCovered?: string[];
  };
} 