#!/bin/sh

# ci_post_xcodebuild.sh
# Post-build script for Xcode Cloud - ProROLA iOS

set -e

echo "🎉 ProROLA iOS - Post-Xcodebuild processing..."

# Check if build was successful
if [ "$CI_XCODEBUILD_EXIT_CODE" = "0" ]; then
    echo "✅ Xcode build completed successfully!"
    
    # Display build information
    echo "📋 Build Information:"
    echo "   - Product Name: $CI_PRODUCT"
    echo "   - Scheme: $CI_XCODE_SCHEME"
    echo "   - Configuration: $CI_XCODE_CONFIGURATION"
    echo "   - Archive Path: $CI_ARCHIVE_PATH"
    
    # Check if archive exists
    if [ -n "$CI_ARCHIVE_PATH" ] && [ -d "$CI_ARCHIVE_PATH" ]; then
        echo "📦 Archive created at: $CI_ARCHIVE_PATH"
        
        # List archive contents
        echo "📋 Archive contents:"
        ls -la "$CI_ARCHIVE_PATH"
        
        # Check for app bundle
        APP_PATH="$CI_ARCHIVE_PATH/Products/Applications"
        if [ -d "$APP_PATH" ]; then
            echo "📱 App bundle found:"
            ls -la "$APP_PATH"
        fi
    else
        echo "⚠️  Archive path not found or empty"
    fi
    
    # Display build artifacts
    if [ -n "$CI_DERIVED_DATA_PATH" ]; then
        echo "🔍 Build artifacts location: $CI_DERIVED_DATA_PATH"
    fi
    
else
    echo "❌ Xcode build failed with exit code: $CI_XCODEBUILD_EXIT_CODE"
    exit 1
fi

echo "🏁 Post-build processing completed!"
