#!/bin/sh

# ci_post_clone.sh
# Xcode Cloud build script for ProROLA iOS React Native Expo app

set -e

echo "🚀 ProROLA iOS Build - Starting Xcode Cloud setup..."

# Set Node.js version (adjust as needed for your project)
NODE_VERSION="18"

echo "🔧 Setting up Node.js environment..."

# Install Node.js using nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Install and use specified Node.js version
nvm install $NODE_VERSION
nvm use $NODE_VERSION

# Verify Node.js installation
echo "📋 Node.js version: $(node --version)"
echo "📋 npm version: $(npm --version)"

echo "📦 Installing dependencies..."

# Clean install dependencies
npm ci --production=false

echo "🏗️ Installing Expo CLI..."

# Install Expo CLI globally
npm install -g @expo/cli@latest

# Verify Expo CLI installation
echo "📋 Expo CLI version: $(npx expo --version)"

echo "📱 Pre-building iOS project..."

# Prebuild the iOS project for Xcode Cloud
npx expo prebuild --platform ios --clean --no-install

echo "🔍 Verifying iOS project structure..."

# Check if iOS project was created successfully
if [ -d "ios" ]; then
    echo "✅ iOS project created successfully"
    ls -la ios/
else
    echo "❌ iOS project creation failed"
    exit 1
fi

echo "🎯 Installing iOS dependencies..."

# Navigate to iOS directory and install pods
cd ios
pod install --repo-update
cd ..

echo "✅ Xcode Cloud setup completed successfully!"
echo "📱 Ready for iOS build process..."
